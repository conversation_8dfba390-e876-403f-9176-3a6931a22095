
<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>CyberShelter</title>

<link rel="icon" type="image/png" href="/favicon.ico">

<link rel="preconnect" href="https://fonts.googleapis.com/">
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,400;9..40,500;9..40,600;9..40,700;9..40,800&amp;display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@500;600;700&amp;display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Castoro&amp;display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Schoolbell&amp;display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/bootstrap/css/bootstrap.min.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/bootstrap-select/bootstrap-select.min.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/animate/animate.min.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/fontawesome/css/all.min.css" />

<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/jquery-ui/jquery-ui.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/jarallax/jarallax.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/jquery-magnific-popup/jquery.magnific-popup.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/nouislider/nouislider.min.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/nouislider/nouislider.pips.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/tiny-slider/tiny-slider.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/cleenhearts-icons/style.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/swiper/css/swiper-bundle.min.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/owl-carousel/css/owl.carousel.min.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/vendors/owl-carousel/css/owl.theme.default.min.css" />
<link rel="stylesheet" href="https://www.cybershelter.com/assets_web/css/style.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

<!-- Font performance optimizations -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<link href="https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Source+Sans+3:ital,wght@0,200..900;1,200..900&family=Titillium+Web:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700&display=swap" rel="stylesheet">


<link href="https://www.cybershelter.com/assets_web/css/main.css" rel="stylesheet" type="text/css">
<style>
    :root {


        --cleenhearts-text: #757277;
        --cleenhearts-text-rgb: 117, 114, 119;
        --cleenhearts-text-gray: #AA96B4;
        --cleenhearts-text-gray-rgb: 170, 150, 180;
        --cleenhearts-text-gray2: #888888;
        --cleenhearts-text-gray2-rgb: 136, 136, 136;
        --cleenhearts-base: #1e6672;
        --cleenhearts-base-rgb: 53, 28, 66;
        --cleenhearts-secondary: #b9d989 !important;
    }

    body {
        font-family: "Titillium Web", serif;
        background-image: url('https://www.cybershelter.com/assets_web/images-2/new/bgg.webp') !important;
    }

    header {
        width: 100%;
        max-width: 100% !important;
    }

    .about-three__text {
        text-transform: none !important;
    }

    body {
        font-family: "Titillium Web", serif !important;
    }

    @media (max-width: 600px) {
        .header-nav .nav>li .mega-menu {
            display: block;
        }

        .about-info-two__logo {

            display: none !important;
        }

        .volunteer-details__background__Inner {
            text-align: center !important;
        }

        .caption p {
            text-align: justify !important;
        }

        .event-details__content {
            text-align: justify;
        }

        .main-banner .caption p {
            margin-bottom: 10px !important;
        }

        .volunteer-details__background__text {
            text-align: justify !important;
            padding: 15px;
        }

        .volunteer-details__background__heading {
            text-align: center !important;
            font-size: 34px;
            margin-top: 20px;
        }

        .service-one__content__inner:first-child .service-one__item {
            padding-top: 10px !important;
        }

        .story-one__content {

            height: auto !important;
        }
    }

    @media screen and (max-width: 767px) {
        @media (max-width: 600px) {
            .header-nav .nav>li .mega-menu {
                display: block !important;
            }
        }
    }

    @media only screen and (max-width: 991px) {

        .header-nav .nav>li .sub-menu,
        .header-nav .nav>li .mega-menu {
            border-radius: 0;
        }
    }

    @media only screen and (max-width: 991px) {
        .header-nav .nav>li .mega-menu {
            border: 0;
        }
    }

    .header-nav .nav>li .mega-menu {
        background: rgba(17, 29, 32, 10);
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(5px) !important;
        -webkit-backdrop-filter: blur(5px) !important;
        border: 1px solid rgba(17, 29, 32, 0.5);
        display: flex;
        left: 0px;
        list-style: none;
        opacity: 2;
        position: absolute;
        right: 0px;
        visibility: hidden;
        width: 100%;
        margin-top: 0px !important;
        z-index: 9;
        padding: 10px !important;
        gap: 50px;
    }

    .header-nav .nav>li>a {
        border-radius: 0px;
        color: #fff;
        font-size: 16px;
        padding: 5px 14px !important;
        cursor: pointer;
        font-weight: 500;
        display: inline-block;
        position: relative;
        /* border: 1px solid rgba(0, 0, 0, 0.10); */
        border-width: 0 0 0 1px;
    }

    .main-banner .caption p {
        color: #fff;
        margin-bottom: 0px !important;
    }

    .sec-title__tagline {
        display: none !important;
        position: relative;
        margin: 0;
        font-family: var(--cleenhearts-font, "DM Sans", sans-serif);
        color: #b9d989 !important;
        font-weight: bold;
        font-size: 14px;
        line-height: 1;
        text-transform: uppercase;
        margin-bottom: 21px;
        margin-left: 24px;
    }

    p {
        font-family: var(--cleenhearts-font, "DM Sans", sans-serif);
        font-size: 16px;
        color: #fff;
        font-weight: 400;
        line-height: 1.75;
        text-transform: none !important;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-family: "Titillium Web", serif;
        color: var(--cleenhearts-base, #351C42);
        font-weight: 700;
        text-transform: inherit !important;

    }

    .blog-card-two__image__link {
        position: relative;
        overflow: hidden;
        display: block;
        width: 100% !important;
        border-radius: 20px;
        overflow: hidden;
    }

    .sec-title__tagline::before {
        display: none !important;
        content: "";
        position: absolute;
        top: 50%;
        left: -24px;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border-radius: 50%;
        /* background-color: var(--cleenhearts-purple, #965995); */
        z-index: 1;
    }

    .sec-title__tagline::after {
        display: none !important;
        content: "";
        position: absolute;
        top: 50%;
        left: -24px;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: var(--cleenhearts-purple, #965995);
        z-index: 1;
    }

    .team-single::before {
        content: "";
        position: absolute;
        top: -25px;
        left: 0;
        width: 100%;
        height: 100%;
        /* border-radius: 1000px 1000px 20px 20px; */
        border: 2px solid #b9d989 !important;
        transition: all 500ms ease;
        z-index: -1;
    }

    .blog-card-three .cleenhearts-btn__icon-box {
        background-color: #b9d989 !important;
    }

    @media (min-width: 768px) {
        .logo-header.d-md-none {
            display: block !important;
        }
    }

    @media only screen and (max-width: 991px) {
        .header-nav .nav>li .mega-menu>li {
            padding: 0px 0;
        }
    }





    .event-card-four {
        height: auto !important;
        margin-bottom: 20px !important;
    }
</style>

<script type="b504f74f83a97a3fca55ee88-text/javascript">
    document.addEventListener("DOMContentLoaded", function() {
        var img1 = document.getElementById("manual-image1");
        var img2 = document.getElementById("manual-image2");
        img1.src = img1.getAttribute("data-src");
        img2.src = img2.getAttribute("data-src");
    });
</script>
    <style>
        #prev-controller,
        #next-controller {
            display: none !important;
        }

        .section-to-scroll {
            position: relative;
            overflow: hidden;
            height: 120vh;

        }







        @media (max-width: 767px) and (orientation: portrait) {
            .di-main {
                width: 94%;
                top: 40% !important;
            }

            .section-to-scroll {
                position: relative;
                overflow: hidden;
                height: 80vh;

            }
        }
    </style>


    <!-- top section style -->
    <style>
        .banner-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            /* Removed the old background-image property */
        }

        /* NEW: Background Video Styling */
        .background-video {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 0;
        }

        /* NEW: Video Overlay for Text Readability */
        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.7));
            z-index: 1;
        }

        /* UPDATED: Business background effect z-index */
        .banner-container::before {
            content: '';
            position: absolute;
            inset: 0;
            background: radial-gradient(circle at 30% 40%, rgba(100, 116, 139, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 70% 60%, rgba(71, 85, 105, 0.2) 0%, transparent 50%);
            z-index: 2;
            /* Changed from z-index: 1 to z-index: 2 */
        }

        .main-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            margin-top: 70px;
        }

        .circular-component {
            position: relative;
            width: 100%;
            max-width: 700px;
            margin: 0 auto;
            aspect-ratio: 1/1;
        }

        .central-content {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 5;
        }

        .content-inner {
            text-align: center;
            transition: opacity 0.4s ease-in-out;
        }

        .content-inner.transitioning {
            opacity: 0;
        }

        .main-title {
            font-size: 4rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
            line-height: 1;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .subtitle-line {
            font-size: 4rem;
            font-weight: 700;
            color: white;
            line-height: 1;
            margin-bottom: 0.25rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .subtitle-line:last-of-type {
            margin-bottom: 1.5rem;
        }

        .cta-button {
            background: rgba(31, 41, 55, 0.9);
            backdrop-filter: blur(10px);
            color: white;
            padding: 1rem 2.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.125rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .cta-button:hover {
            background: rgba(55, 65, 81, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        .svg-container {
            position: absolute;
            inset: 0;
            z-index: 1;
        }

        .circular-svg {
            width: 100%;
            height: 100%;
        }

        /* Circle styling */
        .circle-border {
            fill: none;
            stroke: rgba(255, 255, 255, 0.4);
            stroke-width: 2;
            stroke-dasharray: 8 8;
        }

        .progress-ring {
            fill: none;
            stroke: #b9d989;
            stroke-width: 3;
            stroke-linecap: round;
            transition: stroke-dasharray 1s ease-out;
        }

        .tick-mark {
            stroke: rgba(255, 255, 255, 0.8);
            stroke-width: 1;
            stroke-linecap: round;
        }

        .tick-mark-bold {
            stroke: rgba(255, 255, 255, 1);
            stroke-width: 2;
            stroke-linecap: round;
            display: none;
        }

        .tick-mark-small {
            stroke: rgba(255, 255, 255, 0.4);
            stroke-width: 1;
            stroke-linecap: round;
        }

        /* Animations */
        @keyframes pulse {

            0%,
            100% {
                opacity: 0.3;
            }

            50% {
                opacity: 0.5;
            }
        }

        @keyframes ping {
            0% {
                opacity: 0.3;
                transform: scale(1);
            }

            75% {
                opacity: 0;
                transform: scale(1.5);
            }

            100% {
                opacity: 0;
                transform: scale(1.5);
            }
        }

        .pulse-slow {
            animation: pulse 3s ease-in-out infinite;
        }

        .ping {
            animation: ping 2s ease-in-out infinite;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 480px) {

            .main-title,
            .subtitle-line {
                font-size: 1.5rem;
            }

            .cta-button {
                padding: 0.5rem 1.25rem;
                font-size: 0.8rem;
                gap: 0.5rem;
            }

            .main-container {
                padding: 1rem;
            }

            .circular-component {
                max-width: 400px;
            }
        }

        @media (min-width: 481px) and (max-width: 640px) {

            .main-title,
            .subtitle-line {
                font-size: 1.875rem;
            }

            .circular-component {
                max-width: 500px;
            }

            .cta-button {
                padding: 0.625rem 1.5rem;
                font-size: 0.875rem;
            }
        }

        @media (min-width: 641px) and (max-width: 768px) {

            .main-title,
            .subtitle-line {
                font-size: 2.5rem;
            }

            .circular-component {
                max-width: 600px;
            }

            .cta-button {
                padding: 0.75rem 1.75rem;
                font-size: 0.95rem;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {

            .main-title,
            .subtitle-line {
                font-size: 3.5rem;
            }

            .circular-component {
                max-width: 700px;
            }

            .cta-button {
                padding: 0.875rem 2rem;
                font-size: 1rem;
            }
        }

        @media (min-width: 1025px) and (max-width: 1280px) {

            .main-title,
            .subtitle-line {
                font-size: 4rem;
            }

            .circular-component {
                max-width: 700px;
            }

            .cta-button {
                padding: 1rem 2.25rem;
                font-size: 1.075rem;
            }
        }

        @media (min-width: 1281px) and (max-width: 1536px) {

            .main-title,
            .subtitle-line {
                font-size: 4.5rem;
            }

            .circular-component {
                max-width: 700px;
            }

            .cta-button {
                padding: 1rem 2.5rem;
                font-size: 1.125rem;
            }
        }

        @media (min-width: 1537px) {

            .main-title,
            .subtitle-line {
                font-size: 4rem;
            }

            .circular-component {
                max-width: 800px;
            }

            .cta-button {
                padding: 1.125rem 2.75rem;
                font-size: 1.25rem;
            }
        }

        /* Landscape and touch optimizations */
        @media (max-height: 600px) and (orientation: landscape) {
            .banner-container {
                min-height: 75vh;
                padding: 1rem 0;
            }

            .main-title,
            .subtitle-line {
                font-size: 1.5rem !important;
            }

            .circular-component {
                max-width: 400px !important;
            }

            .subtitle-line:last-of-type {
                margin-bottom: 1.5rem;
            }

        }

        @media (hover: none) and (pointer: coarse) {
            .cta-button:hover {
                transform: none;
            }
        }
    </style>

    <script type="b504f74f83a97a3fca55ee88-text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            // Get video elements
            const videoEven = document.getElementById('video-container-even');
            const videoOdd = document.getElementById('video-container-odd');

            // Force play the videos
            function playVideos() {
                videoEven.play().catch(e => console.log('Video play error:', e));
                videoOdd.play().catch(e => console.log('Video play error:', e));
            }

            // Try to play immediately
            playVideos();

            // Some browsers require user interaction first
            document.body.addEventListener('click', function() {
                playVideos();
            }, {
                once: true
            });



        });
    </script>

</head>










<body class="section-scroll-enable
      
       normal-mode corporate-site page_a53bb6ab72274169ac31cc6769e98074" style="">

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebSite",
            "url": "https://ghgH5NTNNlov.com/",
            "potentialAction": {
                "@type": "SearchAction",
                "target": "https://ghgH5NTNNlov.com/search?indexCatalogue=site-search-index&searchQuery={search_term_string}",
                "query-input": "required name=search_term_string"
            }
        }
    </script>

    <div class="page-wrapper">

        <header class="site-header mo-left header"
  style="height: 100px; background: rgba(200, 211, 213, 0.03); box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1); backdrop-filter: blur(11.6px); -webkit-backdrop-filter: blur(11.6px); width: 100%;">
  <div class="sticky-header main-bar-wraper navbar-expand-lg"
    style="height: 79px; background: rgba(200, 211, 213, 0.03); box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1); backdrop-filter: blur(11.6px); -webkit-backdrop-filter: blur(11.6px); height: 100px;">
    <div class="main-bar clearfix">
      <div class="container clearfix">
        <div class="d-flex justify-content-between" style="gap:150px;">

          <div class="logo-header d-block d-lg-none">
            <a href="https://www.cybershelter.com/home">
              <img data-src="https://www.cybershelter.com/assets_web/images-2/new/CyberShelter-optimized.png"
                alt="" style="width: 200px;" class="mt-2 mb-2" id="manual-image1">
            </a>
          </div>

          <button class="navbar-toggler navicon mt-5" type="button" data-bs-toggle="collapse"
            data-bs-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false"
            aria-label="Toggle navigation">
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>

        <div class="header-nav navbar-collapse collapse" id="navbarNavDropdown"
          style="background-color: transparent; justify-content: space-between;">

          <!-- Tablet and Desktop Logo -->
          <div class="logo-header d-none d-md-block">
            <a href="https://www.cybershelter.com/">
              <img data-src="https://www.cybershelter.com/assets_web/images-2/new/CyberShelter-optimized.png"
                alt="" style="width: 200px;" class="mt-2 mb-2" id="manual-image2">
            </a>
          </div>

          <!-- Navigation -->
          <div style="display: flex; flex-direction: column;">
            <ul class="nav navbar-nav">
              <li><a href="https://www.cybershelter.com/" style="color: white; text-decoration: none;">Home</a></li>

              <li class="has-mega-menu">
                <a href="#" style="color: white; text-decoration: none;">Who We Are <i class="fa fa-chevron-down"></i></a>
                <ul class="mega-menu mt-0">
                  <li>
                    <ul>
                      <li><a href="https://www.cybershelter.com/founded">Founded by Industry Stalwarts</a></li>
                      <li><a href="https://www.cybershelter.com/industry">Industry Experience</a></li>
                      <li><a href="https://www.cybershelter.com/fundamentals">Based on Fundamentals</a></li>
                      <li><a href="https://www.cybershelter.com/business-understanding">Business Understanding</a></li>
                      <li><a href="https://www.cybershelter.com/flexible">Flexible</a></li>
                      <li><a href="https://www.cybershelter.com/vision-mission">Vision and Mission</a></li>
                      <li><a href="https://www.cybershelter.com/holistic">Holistic</a></li>
                      <li><a href="https://www.cybershelter.com/agile">Agile</a></li>
                      <li><a href="https://www.cybershelter.com/practical">Practical</a></li>
                    </ul>
                  </li>
                  <li class="d-none d-md-block">
                    <ul>
                      <li class="nav_title" style="font-size: 28px; padding: 10px 25px; color: #fff;">Comprehensive Cybersecurity Solutions</li>
                      <li>
                        <img src="https://www.cybershelter.com/assets_web/images-2/240_F_480884814_busW8r7P4VroKSGjCupiyBekzGT61kVF.jpg"
                          alt="Cybersecurity Services" style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">We provide a full range of cybersecurity services...</p>
                      </li>
                    </ul>
                  </li>
                  <li class="d-none d-md-block">
                    <ul>
                      <li class="nav_title" style="font-size: 28px; padding: 10px 25px; color: #fff;">Managed Security Services</li>
                      <li>
                        <img src="https://www.cybershelter.com/assets_web/images-2/business-cyber.jpg" alt="Managed Security"
                          style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">Our managed security services provide round-the-clock monitoring...</p>
                      </li>
                    </ul>
                  </li>
                </ul>
              </li>

              <li class="has-mega-menu">
                <a href="#" style="color: white; text-decoration: none;">What We Do <i class="fa fa-chevron-down"></i></a>
                <ul class="mega-menu mt-0">
                  <li>
                    <ul>
                      <li><a href="https://www.cybershelter.com/professional-services">Professional Services</a></li>
                      <li><a href="https://www.cybershelter.com/managed-services">Managed Services</a></li>
                      <li><a href="https://www.cybershelter.com/solutions">Solutions</a></li>
                    </ul>
                  </li>
                  <li class="d-none d-md-block">
                    <ul>
                      <li class="nav_title" style="font-size: 28px; padding: 10px 25px; color: #fff;">Penetration Testing</li>
                      <li>
                        <img src="https://www.cybershelter.com/assets_web/images-2/21839.jpg" alt="Penetration Testing"
                          style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">We conduct thorough penetration testing...</p>
                      </li>
                    </ul>
                  </li>
                  <li class="d-none d-md-block">
                    <ul>
                      <li class="nav_title" style="font-size: 28px; padding: 10px 25px; color: #fff;">Compliance & Risk Management</li>
                      <li>
                        <img src="https://www.cybershelter.com/assets_web/images-2/news-1.webp" alt="Compliance and Risk"
                          style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">We help your organization meet industry standards...</p>
                      </li>
                    </ul>
                  </li>
                </ul>
              </li>

              <li class="has-mega-menu">
                <a href="#" style="color: white; text-decoration: none;">Buzz/What’s hot <i class="fa fa-chevron-down"></i></a>
                <ul class="mega-menu mt-0">
                  <li>
                    <ul>
                      <li><a href="https://www.cybershelter.com/ai-security">AI Security</a></li>
                      <li><a href="https://www.cybershelter.com/api-security">API Security</a></li>
                      <li><a href="https://www.cybershelter.com/trpm">TPRM</a></li>
                    </ul>
                  </li>
                  <li class="d-none d-md-block">
                    <ul>
                      <li class="nav_title" style="font-size: 28px; padding: 10px 25px; color: #fff;">Latest Cybersecurity Threats</li>
                      <li>
                        <img src="https://www.cybershelter.com/assets_web/images-2/view-people-addicted-their-smartphone-looking-scrolling-through-screens.jpg"
                          alt="Cybersecurity Threats" style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">Stay informed about the latest cybersecurity threats...</p>
                      </li>
                    </ul>
                  </li>
                  <li class="d-none d-md-block">
                    <ul>
                      <li class="nav_title" style="font-size: 28px; padding: 10px 25px; color: #fff;">Cybersecurity Industry Insights</li>
                      <li>
                        <img src="https://www.cybershelter.com/assets_web/images-2/medium-shot-woman-working-computer_23-2150287666.webp"
                          alt="Cybersecurity Insights" style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">Get the latest industry insights, trends, and best practices...</p>
                      </li>
                    </ul>
                  </li>
                </ul>
              </li>

              <li><a href="https://www.cybershelter.com/careers" style="color: white; text-decoration: none;">Careers</a></li>
              <li><a href="https://www.cybershelter.com/contacts" style="color: white; text-decoration: none;">Contact</a></li>
            </ul>
          </div>
        </div>

      </div>
    </div>
  </div>
</header>
        <div>
            <div class="hero-banner" style="background:transparent!important;">
                <div id="main" class="home-main">
                    <div id="MainTop_T03A6BEBD001_Col00" class="sub-main-banner sf_colsIn" data-sf-element="Content"
                        data-placeholder-label="Content">

                        <div class="shadow">
                            <div class="mobile-gradient"></div>
                        </div>


                        <div>


                            <!-- <div class="section-to-scroll" data-color="dark" id="Home" data-section-name="Home" style="-->
                            <!-- background: url('https://www.cybershelter.com/assets_web/images-2/new/banner-main-2.gif') no-repeat center center;!important;-->
                            <!-- background-size: cover;-->
                            <!-- background-position: center;-->
                            <!-- background-repeat: no-repeat;padding-bottom:50px;">-->
                            <div class="section-to-scroll" data-color="dark" id="Home" style="
                        background: url('') no-repeat center center;
                        background-size: cover;
                        background-position: center;
                        background-repeat: no-repeat;">


                                <h1 class=" hidden-title">Cyber Shelter</h1>
                                <div id="video-container" class="vid-container">
                                    <button id="close" class="close-btn no-display">X</button>
                                    <div id="video-even" class="vid-even video-track">
                                        <video autoplay playsinline muted loop id="video-container-even" preload="metadata" class="bg-video">
                                            <source src="https://www.cybershelter.com/assets_web/images-2/new/video-2.webm" type="video/webm">
                                            <source src="https://www.cybershelter.com/assets_web/images-2/new/video-2.mp4" type="video/mp4">
                                            Your browser does not support HTML5 video.
                                        </video>
                                    </div>

                                    <div id="video-odd" class="vid-odd video-track">
                                        <video autoplay playsinline muted loop id="video-container-odd" preload="metadata" class="bg-video">
                                            <source src="https://www.cybershelter.com/assets_web/images-2/new/video-2.webm" type="video/webm">
                                            <source src="https://www.cybershelter.com/assets_web/images-2/new/video-2.mp4" type="video/mp4">
                                            Your browser does not support HTML5 video.
                                        </video>
                                    </div>
                                    <div class="di-main no-display" id="dial-main"
                                        style="margin-top: 50px;width: 713px;display: block;">
                                        <ul class="di-main-hotspots-svg" id="dial-main-hotspots-svg">
                                            <li class="hotspot main-hotspot ripple
                            
                                            " id="main-hotspot-0" data-hour="0" data-quotes="#" data-cta-url="#"
                                                data-video-src="https://www.cybershelter.com/assets_web/images-2/new/video-2.webm"
                                                data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/video-2.webm"
                                                style="position:absolute;z-index:99999!important;">

                                            </li>

                                            <li class="hotspot main-hotspot ripple
                            
                            " id="main-hotspot-6" data-hour="6" data-quotes="#" data-cta-url="#" data-video-src="#"
                                                data-video-src-mobile="#">

                                            </li>


                                            <li class="hotspot main-hotspot ripple
                            
                            " id="main-hotspot-12" data-hour="12" data-quotes="#" data-cta-url="#" data-video-src="#"
                                                data-video-src-mobile="#">

                                            </li>

                                            <li class="hotspot main-hotspot ripple
                            
                            " id="main-hotspot-18" data-hour="18" data-quotes="#" data-cta-url="#" data-video-src="#"
                                                data-video-src-mobile="#">

                                            </li>

                                        </ul>
                                        <div class="di-main-wrapper" style="opacity:0.5;">
                                            <div id="welcome-msg" class="content-dial no-display">
                                                <h2 id="welcome-msg-text" class="content-dial-txt">
                                                    Safeguarding Your Digital Realm</h2>
                                            </div>

                                            <svg class="di-main-svg no-display" id="dial-main-svg" viewBox="0 0 100 100"

                                                style="transform: rotate(0deg) scale(1); display: inline; touch-action: none;width: 100%;">

                                                <path id="dial-progress-value" class="di-progress-value"
                                                    fill="rgba(0,0,0,0)" stroke="rgba(175, 214, 137, 1)"
                                                    stroke-width="0.4"
                                                    d="M 94.43258800929715 38.094323925284044 A 46 46 0 0 0 50 4">
                                                </path>
                                                <g id="rotate-area"
                                                    transform="matrix(-0.79863,0.60181,-0.60181,-0.79863,120.02252665996706,59.84102434476223)">
                                                    <circle id="dial-progress-path" class="di-progress-bar"
                                                        stroke-width="1" cx="50" cy="50" r="50" fill="rgba(0,0,0,0)"
                                                        stroke="rgba(0,0,0,0)">
                                                    </circle>
                                                </g>
                                                <circle id="dial-drag-patch" class="drag-patch" stroke-width="1" cx="50"
                                                    cy="50" r="45" fill="rgba(0,0,0,0)" stroke="rgba(0,0,0,0)"></circle>
                                                <defs id="SvgjsDefs1006"></defs>
                                                <circle cx="50" cy="4" r="1.3" fill="#ccc" transform="rotate(0,50,50)"
                                                    class="inner-point base-fill gr-dot main-dot" id="inner-pt-90">
                                                </circle>
                                                <rect x="49.4" y="-1" fill="rgba(175, 214, 137, 1)"
                                                    transform="rotate(0,50,50)" class="hotspot m-hp ripple"
                                                    height="3.300000000000001px" width="0.8999999999999999px"
                                                    id="dash-90"></rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(-1,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-90" data-hour="6"
                                                    data-quotes="Quick, Efficient, and Adaptive Solutions"
                                                    data-cta-url="https://www.cybershelter.com/holistic"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/video-2.webm"
                                                    data-video-src-mobile="">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)"
                                                    transform="rotate(1.5,50,50)" class="inner-point"
                                                    id="inner-pt-91.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(1.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(3,50,50)"
                                                    class="inner-point" id="inner-pt-93"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(3,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(4.5,50,50)"
                                                    class="inner-point" id="inner-pt-94.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(4.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(6,50,50)"
                                                    class="inner-point" id="inner-pt-96"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(6,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(7.5,50,50)"
                                                    class="inner-point" id="inner-pt-97.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(7.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-97.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(9,50,50)"
                                                    class="inner-point" id="inner-pt-99"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(9,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(10.5,50,50)" class="inner-point"
                                                    id="inner-pt-100.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(10.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(12,50,50)"
                                                    class="inner-point" id="inner-pt-102"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(12,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(13.5,50,50)" class="inner-point"
                                                    id="inner-pt-103.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(13.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(15,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-105"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(15,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-105">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(14,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-105" data-hour="7" data-quotes="Industry
Experience" data-cta-url="https://www.cybershelter.com/industry"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/video-2.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/practical.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(16.5,50,50)" class="inner-point"
                                                    id="inner-pt-106.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(16.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(18,50,50)"
                                                    class="inner-point" id="inner-pt-108"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(18,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(19.5,50,50)" class="inner-point"
                                                    id="inner-pt-109.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(19.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(21,50,50)"
                                                    class="inner-point" id="inner-pt-111"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(21,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(22.5,50,50)" class="inner-point"
                                                    id="inner-pt-112.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(22.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-112.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(24,50,50)"
                                                    class="inner-point" id="inner-pt-114"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(24,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(25.5,50,50)" class="inner-point"
                                                    id="inner-pt-115.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(25.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(27,50,50)"
                                                    class="inner-point" id="inner-pt-117"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(27,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(28.5,50,50)" class="inner-point"
                                                    id="inner-pt-118.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(28.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(30,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-120"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(30,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-120">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(29,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-120" data-hour="8"
                                                    data-quotes="Strategic Security for Complex Challenges"
                                                    data-cta-url="business.html"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/flexible.webm"
                                                    data-video-src-mobile="">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(31.5,50,50)" class="inner-point"
                                                    id="inner-pt-121.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(31.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(33,50,50)"
                                                    class="inner-point" id="inner-pt-123"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(33,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(34.5,50,50)" class="inner-point"
                                                    id="inner-pt-124.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(34.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(36,50,50)"
                                                    class="inner-point" id="inner-pt-126"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(36,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(37.5,50,50)" class="inner-point"
                                                    id="inner-pt-127.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(37.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-127.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(39,50,50)"
                                                    class="inner-point" id="inner-pt-129"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(39,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(40.5,50,50)" class="inner-point"
                                                    id="inner-pt-130.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(40.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(42,50,50)"
                                                    class="inner-point" id="inner-pt-132"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(42,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(43.5,50,50)" class="inner-point"
                                                    id="inner-pt-133.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(43.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(45,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-135"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(45,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-135">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(44,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-135" data-hour="9"
                                                    data-quotes="Empowering Businesses with Confidence"
                                                    data-cta-url="https://www.cybershelter.com/business-understanding"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/video-1.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/video-1.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(46.5,50,50)" class="inner-point"
                                                    id="inner-pt-136.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(46.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(48,50,50)"
                                                    class="inner-point" id="inner-pt-138"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(48,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(49.5,50,50)" class="inner-point"
                                                    id="inner-pt-139.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(49.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(51,50,50)"
                                                    class="inner-point" id="inner-pt-141"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(51,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(52.5,50,50)" class="inner-point"
                                                    id="inner-pt-142.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(52.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-142.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(54,50,50)"
                                                    class="inner-point" id="inner-pt-144"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(54,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(55.5,50,50)" class="inner-point"
                                                    id="inner-pt-145.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(55.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(57,50,50)"
                                                    class="inner-point" id="inner-pt-147"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(57,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(58.5,50,50)" class="inner-point"
                                                    id="inner-pt-148.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(58.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(60,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-150"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(60,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-150">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(59,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-150" data-hour="10" data-quotes="Flexible"
                                                    data-cta-url="https://www.cybershelter.com/flexible"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/flexible.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/flexible.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(61.5,50,50)" class="inner-point"
                                                    id="inner-pt-151.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(61.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(63,50,50)"
                                                    class="inner-point" id="inner-pt-153"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(63,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(64.5,50,50)" class="inner-point"
                                                    id="inner-pt-154.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(64.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(66,50,50)"
                                                    class="inner-point" id="inner-pt-156"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(66,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(67.5,50,50)" class="inner-point"
                                                    id="inner-pt-157.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(67.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-157.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(69,50,50)"
                                                    class="inner-point" id="inner-pt-159"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(69,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(70.5,50,50)" class="inner-point"
                                                    id="inner-pt-160.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(70.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(72,50,50)"
                                                    class="inner-point" id="inner-pt-162"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(72,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(73.5,50,50)" class="inner-point"
                                                    id="inner-pt-163.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(73.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6" fill="#fff" transform="rotate(75,50,50)"
                                                    class="inner-point gr-dot" id="inner-pt-165"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(75,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-165">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(74,50,50)"
                                                    class="hotspot-click-area ripple
                          emptysource
                          emptysourcemobile" height="11.1px" width="7.1px" id="dash-click-165" data-hour="11"
                                                    data-quotes="" data-buttontext="" data-cta-url="" data-video-src=""
                                                    data-video-src-mobile="">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(76.5,50,50)" class="inner-point"
                                                    id="inner-pt-166.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(76.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(78,50,50)"
                                                    class="inner-point" id="inner-pt-168"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(78,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(79.5,50,50)" class="inner-point"
                                                    id="inner-pt-169.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(79.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(81,50,50)"
                                                    class="inner-point" id="inner-pt-171"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(81,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(82.5,50,50)" class="inner-point"
                                                    id="inner-pt-172.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(82.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-172.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(84,50,50)"
                                                    class="inner-point" id="inner-pt-174"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(84,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(85.5,50,50)" class="inner-point"
                                                    id="inner-pt-175.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(85.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(87,50,50)"
                                                    class="inner-point" id="inner-pt-177"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(87,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)"
                                                    transform="rotate(88.5,50,50)" class="inner-point"
                                                    id="inner-pt-178.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(88.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="1.3" fill="#ccc" transform="rotate(90,50,50)"
                                                    class="inner-point base-fill gr-dot main-dot" id="inner-pt-180">
                                                </circle>
                                                <rect x="49.4" y="-1" fill="rgba(175, 214, 137, 1)"
                                                    transform="rotate(90,50,50)" class="hotspot m-hp ripple"
                                                    height="3.300000000000001px" width="0.8999999999999999px"
                                                    id="dash-180"></rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(89,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-180" data-hour="12"
                                                    data-quotes="Aligning Cybersecurity with Business Goals"
                                                    data-cta-url="https://www.cybershelter.com/vision-mission"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/video-3.webm"
                                                    data-video-src-mobile="">


                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)"
                                                    transform="rotate(91.5,50,50)" class="inner-point"
                                                    id="inner-pt-181.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(91.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(93,50,50)"
                                                    class="inner-point" id="inner-pt-183"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(93,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(94.5,50,50)" class="inner-point"
                                                    id="inner-pt-184.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(94.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(96,50,50)"
                                                    class="inner-point" id="inner-pt-186"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(96,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(97.5,50,50)" class="inner-point"
                                                    id="inner-pt-187.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(97.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-187.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(99,50,50)"
                                                    class="inner-point" id="inner-pt-189"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(99,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(100.5,50,50)" class="inner-point"
                                                    id="inner-pt-190.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(100.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(102,50,50)"
                                                    class="inner-point" id="inner-pt-192"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(102,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(103.5,50,50)" class="inner-point"
                                                    id="inner-pt-193.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(103.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(105,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-195"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(105,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-195">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(104,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-195" data-hour="13"
                                                    data-quotes="Leaders with Proven Expertise"
                                                    data-cta-url="https://www.cybershelter.com/founded"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/founded.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/founded.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(106.5,50,50)" class="inner-point"
                                                    id="inner-pt-196.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(106.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(108,50,50)"
                                                    class="inner-point" id="inner-pt-198"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(108,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(109.5,50,50)" class="inner-point"
                                                    id="inner-pt-199.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(109.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(111,50,50)"
                                                    class="inner-point" id="inner-pt-201"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(111,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(112.5,50,50)" class="inner-point"
                                                    id="inner-pt-202.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(112.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-202.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(114,50,50)"
                                                    class="inner-point" id="inner-pt-204"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(114,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(115.5,50,50)" class="inner-point"
                                                    id="inner-pt-205.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(115.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(117,50,50)"
                                                    class="inner-point" id="inner-pt-207"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(117,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(118.5,50,50)" class="inner-point"
                                                    id="inner-pt-208.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(118.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(120,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-210"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(120,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-210">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(119,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-210" data-hour="14" data-quotes="Agile"
                                                    data-cta-url="https://www.cybershelter.com/agile"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/agile.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/agile.webm">

                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(121.5,50,50)" class="inner-point"
                                                    id="inner-pt-211.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(121.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(123,50,50)"
                                                    class="inner-point" id="inner-pt-213"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(123,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(124.5,50,50)" class="inner-point"
                                                    id="inner-pt-214.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(124.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(126,50,50)"
                                                    class="inner-point" id="inner-pt-216"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(126,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(127.5,50,50)" class="inner-point"
                                                    id="inner-pt-217.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(127.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-217.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(129,50,50)"
                                                    class="inner-point" id="inner-pt-219"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(129,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(130.5,50,50)" class="inner-point"
                                                    id="inner-pt-220.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(130.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(132,50,50)"
                                                    class="inner-point" id="inner-pt-222"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(132,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(133.5,50,50)" class="inner-point"
                                                    id="inner-pt-223.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(133.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(135,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-225"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(135,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-225">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(134,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-225" data-hour="15"
                                                    data-quotes="Real-World Solutions for Real-World Challenges"
                                                    data-cta-url="#"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/practical.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/practical.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(136.5,50,50)" class="inner-point"
                                                    id="inner-pt-226.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(136.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(138,50,50)"
                                                    class="inner-point" id="inner-pt-228"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(138,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(139.5,50,50)" class="inner-point"
                                                    id="inner-pt-229.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(139.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(141,50,50)"
                                                    class="inner-point" id="inner-pt-231"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(141,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(142.5,50,50)" class="inner-point"
                                                    id="inner-pt-232.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(142.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-232.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(144,50,50)"
                                                    class="inner-point" id="inner-pt-234"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(144,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(145.5,50,50)" class="inner-point"
                                                    id="inner-pt-235.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(145.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(147,50,50)"
                                                    class="inner-point" id="inner-pt-237"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(147,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(148.5,50,50)" class="inner-point"
                                                    id="inner-pt-238.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(148.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(150,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-240"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(150,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-240">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(149,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-240" data-hour="16"
                                                    data-quotes="Vision and Mission"
                                                    data-cta-url="https://www.cybershelter.com/vision-mission"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/video-3.webm"
                                                    data-video-src-mobile="">


                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(151.5,50,50)" class="inner-point"
                                                    id="inner-pt-241.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(151.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(153,50,50)"
                                                    class="inner-point" id="inner-pt-243"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(153,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(154.5,50,50)" class="inner-point"
                                                    id="inner-pt-244.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(154.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(156,50,50)"
                                                    class="inner-point" id="inner-pt-246"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(156,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(157.5,50,50)" class="inner-point"
                                                    id="inner-pt-247.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(157.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-247.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(159,50,50)"
                                                    class="inner-point" id="inner-pt-249"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(159,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(160.5,50,50)" class="inner-point"
                                                    id="inner-pt-250.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(160.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(162,50,50)"
                                                    class="inner-point" id="inner-pt-252"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(162,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(163.5,50,50)" class="inner-point"
                                                    id="inner-pt-253.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(163.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(165,50,50)" class="inner-point gr-dot "
                                                    id="inner-pt-255"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(165,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-255">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(164,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-255" data-hour="17" data-quotes="Founded by Industry
Stalwarts" data-cta-url="https://www.cybershelter.com/founded" data-video-src="https://www.cybershelter.com/assets_web/images-2/new/founded.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/founded.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(166.5,50,50)" class="inner-point"
                                                    id="inner-pt-256.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(166.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(168,50,50)"
                                                    class="inner-point" id="inner-pt-258"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(168,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(169.5,50,50)" class="inner-point"
                                                    id="inner-pt-259.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(169.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(171,50,50)"
                                                    class="inner-point" id="inner-pt-261"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(171,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(172.5,50,50)" class="inner-point"
                                                    id="inner-pt-262.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(172.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-262.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(174,50,50)"
                                                    class="inner-point" id="inner-pt-264"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(174,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(175.5,50,50)" class="inner-point"
                                                    id="inner-pt-265.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(175.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(177,50,50)"
                                                    class="inner-point" id="inner-pt-267"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(177,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(178.5,50,50)" class="inner-point"
                                                    id="inner-pt-268.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(178.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="1.3" fill="#ccc" transform="rotate(180,50,50)"
                                                    class="inner-point base-fill gr-dot main-dot" id="inner-pt-270">
                                                </circle>
                                                <rect x="49.4" y="-1" fill="rgba(175, 214, 137, 1)"
                                                    transform="rotate(180,50,50)" class="hotspot m-hp ripple"
                                                    height="3.300000000000001px" width="0.8999999999999999px"
                                                    id="dash-270"></rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(179,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-270" data-hour="18"
                                                    data-quotes="Driving Innovation, Inspiring Security"
                                                    data-cta-url="https://www.cybershelter.com/flexible"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/flexible-2.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/flexible-2.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)"
                                                    transform="rotate(181.5,50,50)" class="inner-point"
                                                    id="inner-pt-271.5">
                                                </circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(181.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(183,50,50)"
                                                    class="inner-point" id="inner-pt-273"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(183,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(184.5,50,50)" class="inner-point"
                                                    id="inner-pt-274.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(184.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(186,50,50)"
                                                    class="inner-point" id="inner-pt-276"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(186,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(187.5,50,50)" class="inner-point"
                                                    id="inner-pt-277.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(187.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-277.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(189,50,50)"
                                                    class="inner-point" id="inner-pt-279"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(189,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(190.5,50,50)" class="inner-point"
                                                    id="inner-pt-280.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(190.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(192,50,50)"
                                                    class="inner-point" id="inner-pt-282"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(192,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(193.5,50,50)" class="inner-point"
                                                    id="inner-pt-283.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(193.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(195,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-285"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(195,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-285">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(194,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-285" data-hour="19" data-quotes="Industry
Experience" data-cta-url="https://www.cybershelter.com/industry"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/practical.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/practical.webm">


                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(196.5,50,50)" class="inner-point"
                                                    id="inner-pt-286.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(196.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(198,50,50)"
                                                    class="inner-point" id="inner-pt-288"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(198,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(199.5,50,50)" class="inner-point"
                                                    id="inner-pt-289.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(199.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(201,50,50)"
                                                    class="inner-point" id="inner-pt-291"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(201,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(202.5,50,50)" class="inner-point"
                                                    id="inner-pt-292.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(202.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-292.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(204,50,50)"
                                                    class="inner-point" id="inner-pt-294"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(204,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(205.5,50,50)" class="inner-point"
                                                    id="inner-pt-295.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(205.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(207,50,50)"
                                                    class="inner-point" id="inner-pt-297"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(207,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(208.5,50,50)" class="inner-point"
                                                    id="inner-pt-298.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(208.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(210,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-300"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(210,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-300">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(209,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-300" data-hour="20" data-quotes="Practical"
                                                    data-cta-url="https://www.cybershelter.com/practical"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/video-3.webm"
                                                    data-video-src-mobile="">


                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(211.5,50,50)" class="inner-point"
                                                    id="inner-pt-301.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(211.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(213,50,50)"
                                                    class="inner-point" id="inner-pt-303"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(213,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(214.5,50,50)" class="inner-point"
                                                    id="inner-pt-304.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(214.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(216,50,50)"
                                                    class="inner-point" id="inner-pt-306"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(216,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(217.5,50,50)" class="inner-point"
                                                    id="inner-pt-307.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(217.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-307.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(219,50,50)"
                                                    class="inner-point" id="inner-pt-309"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(219,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(220.5,50,50)" class="inner-point"
                                                    id="inner-pt-310.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(220.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(222,50,50)"
                                                    class="inner-point" id="inner-pt-312"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(222,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(223.5,50,50)" class="inner-point"
                                                    id="inner-pt-313.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(223.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(225,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-315"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(225,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-315">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(224,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-315" data-hour="21"
                                                    data-quotes="Building on Strong Foundations"
                                                    data-cta-url="https://www.cybershelter.com/fundamentals"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/flexible-2.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/flexible-2.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(226.5,50,50)" class="inner-point"
                                                    id="inner-pt-316.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(226.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(228,50,50)"
                                                    class="inner-point" id="inner-pt-318"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(228,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(229.5,50,50)" class="inner-point"
                                                    id="inner-pt-319.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(229.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(231,50,50)"
                                                    class="inner-point" id="inner-pt-321"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(231,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(232.5,50,50)" class="inner-point"
                                                    id="inner-pt-322.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(232.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-322.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(234,50,50)"
                                                    class="inner-point" id="inner-pt-324"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(234,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(235.5,50,50)" class="inner-point"
                                                    id="inner-pt-325.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(235.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(237,50,50)"
                                                    class="inner-point" id="inner-pt-327"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(237,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(238.5,50,50)" class="inner-point"
                                                    id="inner-pt-328.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(238.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(240,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-330"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(240,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-330">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(239,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-330" data-hour="22" data-quotes="Business
Understanding" data-cta-url="https://www.cybershelter.com/business-understanding"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/video-1.webm"
                                                    data-video-src-mobile="#">

                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(241.5,50,50)" class="inner-point"
                                                    id="inner-pt-331.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(241.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(243,50,50)"
                                                    class="inner-point" id="inner-pt-333"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(243,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(244.5,50,50)" class="inner-point"
                                                    id="inner-pt-334.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(244.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(246,50,50)"
                                                    class="inner-point" id="inner-pt-336"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(246,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(247.5,50,50)" class="inner-point"
                                                    id="inner-pt-337.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(247.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-337.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(249,50,50)"
                                                    class="inner-point" id="inner-pt-339"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(249,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(250.5,50,50)" class="inner-point"
                                                    id="inner-pt-340.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(250.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(252,50,50)"
                                                    class="inner-point" id="inner-pt-342"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(252,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(253.5,50,50)" class="inner-point"
                                                    id="inner-pt-343.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(253.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(255,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-345"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(255,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-345">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(254,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-345" data-hour="23"
                                                    data-quotes="Based on Fundamentals"
                                                    data-cta-url="https://www.cybershelter.com/fundamentals"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/flexible.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/flexible.webm">

                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(256.5,50,50)" class="inner-point"
                                                    id="inner-pt-346.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(256.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(258,50,50)"
                                                    class="inner-point" id="inner-pt-348"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(258,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(259.5,50,50)" class="inner-point"
                                                    id="inner-pt-349.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(259.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(261,50,50)"
                                                    class="inner-point" id="inner-pt-351"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(261,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(262.5,50,50)" class="inner-point"
                                                    id="inner-pt-352.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(262.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-352.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(264,50,50)"
                                                    class="inner-point" id="inner-pt-354"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(264,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(265.5,50,50)" class="inner-point"
                                                    id="inner-pt-355.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(265.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(267,50,50)"
                                                    class="inner-point" id="inner-pt-357"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(267,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(268.5,50,50)" class="inner-point"
                                                    id="inner-pt-358.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(268.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="1.3" fill="#ccc" transform="rotate(270,50,50)"
                                                    class="inner-point base-fill gr-dot main-dot" id="inner-pt-0">
                                                </circle>
                                                <rect x="49.4" y="-1" fill="rgba(175, 214, 137, 1)"
                                                    transform="rotate(270,50,50)" class="hotspot m-hp ripple"
                                                    height="3.300000000000001px" width="0.8999999999999999px"
                                                    id="dash-0"></rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(269,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-0" data-hour="0"
                                                    data-quotes="Decades of Proven Industry Expertise"
                                                    data-cta-url="https://www.cybershelter.com/industry"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/practical.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/practical.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)"
                                                    transform="rotate(271.5,50,50)" class="inner-point"
                                                    id="inner-pt-1.5">
                                                </circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(271.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(273,50,50)"
                                                    class="inner-point" id="inner-pt-3"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(273,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(274.5,50,50)" class="inner-point"
                                                    id="inner-pt-4.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(274.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(276,50,50)"
                                                    class="inner-point" id="inner-pt-6"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(276,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(277.5,50,50)" class="inner-point"
                                                    id="inner-pt-7.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(277.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-7.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(279,50,50)"
                                                    class="inner-point" id="inner-pt-9"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(279,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(280.5,50,50)" class="inner-point"
                                                    id="inner-pt-10.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(280.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(282,50,50)"
                                                    class="inner-point" id="inner-pt-12"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(282,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(283.5,50,50)" class="inner-point"
                                                    id="inner-pt-13.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(283.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(285,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-15"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(285,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-15">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(284,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-15" data-hour="1"
                                                    data-quotes="Aligning Cybersecurity with Business Goals"
                                                    data-cta-url="https://www.cybershelter.com/vision-mission"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/video-3.webm"
                                                    data-video-src-mobile="">


                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(286.5,50,50)" class="inner-point"
                                                    id="inner-pt-16.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(286.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(288,50,50)"
                                                    class="inner-point" id="inner-pt-18"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(288,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(289.5,50,50)" class="inner-point"
                                                    id="inner-pt-19.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(289.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(291,50,50)"
                                                    class="inner-point" id="inner-pt-21"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(291,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(292.5,50,50)" class="inner-point"
                                                    id="inner-pt-22.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(292.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-22.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(294,50,50)"
                                                    class="inner-point" id="inner-pt-24"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(294,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(295.5,50,50)" class="inner-point"
                                                    id="inner-pt-25.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(295.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(297,50,50)"
                                                    class="inner-point" id="inner-pt-27"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(297,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(298.5,50,50)" class="inner-point"
                                                    id="inner-pt-28.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(298.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(300,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-30"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(300,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-30">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(299,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-30" data-hour="2"
                                                    data-quotes="Tailored Solutions for Every Need"
                                                    data-cta-url="https://www.cybershelter.com/flexible"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/video-2.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/video-2.webm">

                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(301.5,50,50)" class="inner-point"
                                                    id="inner-pt-31.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(301.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(303,50,50)"
                                                    class="inner-point" id="inner-pt-33"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(303,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(304.5,50,50)" class="inner-point"
                                                    id="inner-pt-34.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(304.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(306,50,50)"
                                                    class="inner-point" id="inner-pt-36"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(306,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(307.5,50,50)" class="inner-point"
                                                    id="inner-pt-37.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(307.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-37.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(309,50,50)"
                                                    class="inner-point" id="inner-pt-39"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(309,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(310.5,50,50)" class="inner-point"
                                                    id="inner-pt-40.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(310.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(312,50,50)"
                                                    class="inner-point" id="inner-pt-42"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(312,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(313.5,50,50)" class="inner-point"
                                                    id="inner-pt-43.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(313.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(315,50,50)" class="inner-point gr-dot "
                                                    id="inner-pt-45"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(315,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-45">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(314,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-45" data-hour="3"
                                                    data-quotes="A Comprehensive Approach to Cybersecurity"
                                                    data-cta-url="https://www.cybershelter.com/practical"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/comprohensive.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/comprohensive.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(316.5,50,50)" class="inner-point"
                                                    id="inner-pt-46.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(316.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(318,50,50)"
                                                    class="inner-point" id="inner-pt-48"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(318,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(319.5,50,50)" class="inner-point"
                                                    id="inner-pt-49.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(319.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(321,50,50)"
                                                    class="inner-point" id="inner-pt-51"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(321,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(322.5,50,50)" class="inner-point"
                                                    id="inner-pt-52.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(322.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-52.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(324,50,50)"
                                                    class="inner-point" id="inner-pt-54"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(324,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(325.5,50,50)" class="inner-point"
                                                    id="inner-pt-55.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(325.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(327,50,50)"
                                                    class="inner-point" id="inner-pt-57"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(327,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(328.5,50,50)" class="inner-point"
                                                    id="inner-pt-58.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(328.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(330,50,50)" class="inner-point gr-dot "
                                                    id="inner-pt-60"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(330,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-60">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(329,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-60" data-hour="4"
                                                    data-quotes="Experts You Can Trust, Results You Can Measure"
                                                    data-cta-url="https://www.cybershelter.com/practical"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/founded.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/founded.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(331.5,50,50)" class="inner-point"
                                                    id="inner-pt-61.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(331.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(333,50,50)"
                                                    class="inner-point" id="inner-pt-63"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(333,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(334.5,50,50)" class="inner-point"
                                                    id="inner-pt-64.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(334.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(336,50,50)"
                                                    class="inner-point" id="inner-pt-66"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(336,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(337.5,50,50)" class="inner-point"
                                                    id="inner-pt-67.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(337.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-67.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(339,50,50)"
                                                    class="inner-point" id="inner-pt-69"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(339,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(340.5,50,50)" class="inner-point"
                                                    id="inner-pt-70.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(340.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(342,50,50)"
                                                    class="inner-point" id="inner-pt-72"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(342,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(343.5,50,50)" class="inner-point"
                                                    id="inner-pt-73.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(343.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                    transform="rotate(345,50,50)" class="inner-point gr-dot"
                                                    id="inner-pt-75"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(345,50,50)"
                                                    class="hotspot ripple" height="2.1px" width="0.3px" id="dash-75">
                                                </rect>
                                                <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(344,50,50)"
                                                    class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-75" data-hour="5" data-quotes="Holistic"
                                                    data-cta-url="https://www.cybershelter.com/holistic"
                                                    data-video-src="https://www.cybershelter.com/assets_web/images-2/new/agile.webm"
                                                    data-video-src-mobile="https://www.cybershelter.com/assets_web/images-2/new/agile.webm">
                                                </rect>

                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(346.5,50,50)" class="inner-point"
                                                    id="inner-pt-76.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(346.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(348,50,50)"
                                                    class="inner-point" id="inner-pt-78"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(348,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(349.5,50,50)" class="inner-point"
                                                    id="inner-pt-79.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(349.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(351,50,50)"
                                                    class="inner-point" id="inner-pt-81"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(351,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(352.5,50,50)" class="inner-point"
                                                    id="inner-pt-82.5"></circle>
                                                <rect x="50" y="1" fill="#fff" transform="rotate(352.5,50,50)"
                                                    class="hotspot" height="2.1px" width="0.3px" id="dash-82.5"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(354,50,50)"
                                                    class="inner-point" id="inner-pt-84"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(354,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff"
                                                    transform="rotate(355.5,50,50)" class="inner-point"
                                                    id="inner-pt-85.5"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(355.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(357,50,50)"
                                                    class="inner-point" id="inner-pt-87"></circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(357,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)"
                                                    transform="rotate(358.5,50,50)" class="inner-point"
                                                    id="inner-pt-88.5">
                                                </circle>
                                                <rect x="50" y="1.7999999999999998" fill="#fff"
                                                    transform="rotate(358.5,50,50)" class="inner-dash" height="0.8px"
                                                    width="0.2px"></rect>
                                                <circle id="hotspot-pointer" class="hotspot-pointer-dot" cx="50" cy="4"
                                                    r="1.6" fill="rgba(175, 214, 137, 1)"></circle>
                                            </svg>

                                            <div id="dial-content" class="content-dial no-display">
                                                <div id="mob-hotspot-text" class="mobile-hotspot-text">
                                                    <span></span>
                                                </div>
                                                <h2 id="dial-content-text" class="content-dial-txt"></h2>
                                                <div class="explore-btn">
                                                    <a id="explore" href="#" class="button-explore no-display"
                                                        style="display: inline-block;">
                                                        Know More
                                                    </a>
                                                </div>
                                            </div>
                                            <div id="controls-container">
                                                <!-- Next Controller -->
                                                <button aria-label="Next" id="next-controller">

                                                    <svg width="40" height="40"
                                                        viewBox="0 0 40 40" fill="none">
                                                        <path
                                                            d="M21.35 13.2333L25.7167 17.5999C27 18.8833 27 20.9833 25.7167 22.2666L14.85 33.1333"
                                                            stroke="white" stroke-width="2.5" stroke-miterlimit="10"
                                                            stroke-linecap="round" stroke-linejoin="round"></path>
                                                        <path d="M14.85 6.7334L16.5833 8.46673" stroke="white"
                                                            stroke-width="2.5" stroke-miterlimit="10"
                                                            stroke-linecap="round" stroke-linejoin="round"></path>
                                                    </svg>

                                                </button>

                                                <button aria-label="Previous" id="prev-controller">
                                                    <svg width="40" height="40"
                                                        viewBox="0 0 40 40" fill="none">
                                                        <path
                                                            d="M18.65 13.2333L14.2833 17.5999C13 18.8833 13 20.9833 14.2833 22.2666L25.15 33.1333"
                                                            stroke="white" stroke-width="2.5" stroke-miterlimit="10"
                                                            stroke-linecap="round" stroke-linejoin="round"></path>
                                                        <path d="M25.15 6.7334L23.4167 8.46673" stroke="white"
                                                            stroke-width="2.5" stroke-miterlimit="10"
                                                            stroke-linecap="round" stroke-linejoin="round"></path>
                                                    </svg>
                                                </button>


                                            </div>
                                        </div>
                                    </div>
                                    <!--<div id="video-controls" class="no-display">-->
                                    <!--    <div class="vid-controls">-->
                                    <!--        <div class="video-play">-->
                                    <!--            <button class="play-btn" id="play-pause"></button>-->
                                    <!--        </div>-->
                                    <!--        <div class="vid-progress">-->
                                    <!--            <meter id="video-meter" class="vid-meter" value="100" min="0"-->
                                    <!--                max="100"></meter>-->
                                    <!--        </div>-->
                                    <!--        <div class="equalizer">-->
                                    <!--           ///////////-->
                                    <!--        </div>-->
                                    <!--    </div>-->
                                    <!--</div>-->
                                </div>
                            </div>

                            <script type="b504f74f83a97a3fca55ee88-text/javascript">
                                $(document).ready(function() {

                                    var isPlaying = true; // Variable to track the playback state

                                    // Define SVG icons
                                    var playIcon = `
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
  <path d="M28.55 13.3C34.9333 16.9834 34.9333 23.0167 28.55 26.7L23.4 29.6667L18.25 32.6334C11.8833 36.3167 6.66667 33.3 6.66667 25.9334V20V14.0667C6.66667 6.70003 11.8833 3.68336 18.2667 7.3667L22.0167 9.53336" stroke="white" stroke-width="2.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `;

                                    var pauseIcon = `
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
                        <path d="M8.35 5C5.95 5 5 5.9 5 8.15V31.85C5 34.1 5.95 35 8.35 35H14.4C16.7833 35 17.75 34.1 17.75 31.85V8.15C17.75 5.9 16.8 5 14.4 5" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M31.65 35C34.0333 35 35 34.1 35 31.85V8.15C35 5.9 34.05 5 31.65 5H25.6C23.2167 5 22.25 5.9 22.25 8.15V31.85C22.25 34.1 23.2 35 25.6 35" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `;

                                    $('#play-controller').html(pauseIcon);

                                    // Function to toggle play/pause
                                    function togglePlayPause() {
                                        if (isPlaying) {
                                            stopCurrentVideo();
                                            $('#play-controller').html(playIcon); // Change button to play icon
                                        } else {
                                            playNextVideo();
                                            $('#play-controller').html(pauseIcon); // Change button to pause icon
                                        }
                                        isPlaying = !isPlaying; // Toggle the playback state
                                    }

                                    // Attach event listener for play/pause controller
                                    $('#play-controller').click(function() {
                                        togglePlayPause();
                                    });


                                    // Function to play the next video
                                    function playNextVideo() {
                                        var currentIndex = parseInt($(hotspotPointer).attr("data-current-index"));
                                        var nextIndex = (currentIndex + 1) % 24; // Assuming there are 24 elements
                                        $("#dash-click-" + (nextIndex * 15)).click();
                                    }

                                    // Function to play the previous video
                                    function playPreviousVideo() {
                                        var currentIndex = parseInt($(hotspotPointer).attr("data-current-index"));
                                        var previousIndex = (currentIndex - 1 + 24) % 24; // Assuming there are 24 elements
                                        $("#dash-click-" + (previousIndex * 15)).click();
                                    }

                                    // Function to stop the current video
                                    function stopCurrentVideo() {
                                        // Implement logic to stop the current video playback
                                        // For example, you can pause the video and reset its source
                                        videoEven.pause();
                                        videoEven.src = '';
                                        videoEven.load();
                                        videoOdd.pause();
                                        videoOdd.src = '';
                                        videoOdd.load();
                                        // You may need additional logic here based on your requirements
                                    }

                                    // Attach event listener for next controller
                                    $('#next-controller').click(function() {
                                        playNextVideo();
                                    });

                                    // Attach event listener for previous controller
                                    $('#prev-controller').click(function() {
                                        playPreviousVideo();
                                    });

                                });
                            </script>
                        </div>
                    </div>
                    <div class="scroll-indicator">
                        <span class="arrow"></span>
                    </div>
                </div>
            </div>

            <!-- <div class="banner-container">
                <video class="background-video" autoplay muted loop
                    playsinline>
                    <source src="https://www.cybershelter.com/assets_web/images-2/new/video-2.webm" type="video/mp4">
                    <div style="background: #374151; width: 100%; height: 100%;"></div>
                </video>
                <div class="video-overlay"></div>
                <div class="main-container">
                    <div class="circular-component">
                        <div class="central-content">
                            <div class="content-inner" id="contentInner">
                                <h1 class="main-title" id="mainTitle">Safeguarding</h1>
                                <h2 class="subtitle-line" id="subtitle1">Your Digital Realm</h2>
                                <h2 class="subtitle-line" id="subtitle2"></h2><button class="cta-button"
                                    id="ctaButton"><span id="buttonText">Know More</span><svg width="20"
                                        height="20" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2">
                                        <polyline points="9,18 15,12 9,6"></polyline>
                                    </svg></button>
                            </div>
                        </div>
                        <div class="svg-container"><svg class="circular-svg" viewBox="0 0 600 600">
                                <circle cx="300" cy="300" r="250" class="circle-border" />
                                <circle cx="300" cy="300" r="230" class="circle-border" />
                                <circle id="progressRing" cx="300" cy="300" r="230"
                                    class="progress-ring"
                                    transform="rotate(-90 300 300)"
                                    stroke-dasharray="0 1445" />
                                <g id="pointsContainer"></g>
                            </svg></div>
                    </div>
                </div>
            </div> -->
        </div>



        <section class="gallery-one section-space">
            <div class="container">
                <div class="row gutter-y-40 align-items-center">
                    <div class="col-lg-6">
                        <div class="sec-title">

                            <h6 class="sec-title__tagline">WHAT WE DO</h6>

                            <h3 class="sec-title__title">What we do to <span
                                    class='sec-title__title__inner'>secure</span> your digital world</h3>
                        </div>
                    </div>

                </div>
                <div class="gallery-one__top cleenhearts-owl__carousel cleenhearts-owl__carousel--basic-nav owl-carousel owl-theme owl-loaded owl-drag"
                    data-owl-options="{
                &quot;items&quot;: 1,
                &quot;margin&quot;: 5,
                &quot;loop&quot;: true,
                &quot;smartSpeed&quot;: 700,
                &quot;animateOut&quot;: &quot;fadeOut&quot;,
                &quot;nav&quot;: true,
                &quot;navText&quot;: [&quot;<span class=\&quot;icon-arrow-left\&quot;></span>&quot;,&quot;<span class=\&quot;icon-arrow-right\&quot;></span>&quot;],
                &quot;dots&quot;: false,
                &quot;autoplay&quot;: false,
                &quot;autoplayTimeout&quot;: 5000
            }">

                    <div class="owl-stage-outer">
                        <div class="owl-stage"
                            style="transform: translate3d(-3525px, 0px, 0px); transition: all; width: 12925px;">


                            <div class="owl-item active" style="width: 1170px; margin-right: 5px;">
                                <div class="item">
                                    <div class="gallery-one__top__image"
                                        style="background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, .3), rgba(0, 0, 0, 0.3)),url('https://www.cybershelter.com/assets_web/images-2/new/detective-evaluating-old-archived-folders-organize-all-cases-based-crimes-2.webp');">
                                        <div class="content-wrapper">
                                            <h2>Professional services</h2>
                                            <p>Our expert team provides tailored cybersecurity strategies, risk
                                                assessments, and compliance consulting to strengthen your
                                                organization’s defenses. We deliver professional guidance to protect
                                                your critical assets and ensure operational resilience.</p>
                                            <a href="https://www.cybershelter.com/professional-services" class="btn btn-primary">Learn
                                                More</a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Other Items -->
                            <div class="owl-item" style="width: 1170px; margin-right: 5px;">
                                <div class="item">
                                    <div class="gallery-one__top__image"
                                        style="background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, .3), rgba(0, 0, 0, 0.3)),url('https://www.cybershelter.com/assets_web/images-2/new/multiethnic-colleagues-overseeing-supercomputers-data-center-2.webp');">
                                        <div class="content-wrapper">
                                            <h2>Managed services</h2>
                                            <p>Stay ahead of cyber threats with our proactive monitoring, incident
                                                response, and continuous threat intelligence. Our managed services
                                                ensure your systems are secure 24/7, minimizing downtime and
                                                maximizing peace of mind.</p>
                                            <a href="https://www.cybershelter.com/managed-services" class="btn btn-primary">Learn More</a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="owl-item" style="width: 1170px; margin-right: 5px;">
                                <div class="item">
                                    <div class="gallery-one__top__image"
                                        style="background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, .3), rgba(0, 0, 0, 0.3)),url('https://www.cybershelter.com/assets_web/images-2/new/cyber-security-concept-digital-art-2.webp');">
                                        <div class="content-wrapper">
                                            <h2>Solutions</h2>
                                            <p>We offer cutting-edge solutions, including advanced threat detection,
                                                cloud security, and endpoint protection, designed to safeguard your
                                                digital infrastructure and keep your business running smoothly.</p>
                                            <a href="https://www.cybershelter.com/solutions" class="btn btn-primary">Learn More</a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="owl-item" style="width: 1170px; margin-right: 5px;">
                                <div class="item">
                                    <div class="gallery-one__top__image"
                                        style="background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, .3), rgba(0, 0, 0, 0.3)),url('https://www.cybershelter.com/assets_web/images-2/new/business-teammates-working-late.webp');">
                                        <div class="content-wrapper">
                                            <h2>Buzz</h2>
                                            <p>Stay informed with the latest updates, trends, and insights in the
                                                world of cybersecurity. Our Buzz section keeps you connected with
                                                emerging technologies, industry news, and expert tips to stay ahead
                                                of evolving threats.</p>
                                            <a href="https://www.cybershelter.com/ai-security" class="btn btn-primary">Learn More</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="owl-nav">
                        <button type="button" role="presentation" class="owl-prev" aria-label="carousel button">
                            <span class="icon-arrow-left"></span>
                        </button>
                        <button type="button" role="presentation" class="owl-next" aria-label="carousel button">
                            <span class="icon-arrow-right"></span>
                        </button>
                    </div>


                    <div class="owl-dots disabled"></div>
                </div>
            </div>
        </section>

        <section class="about-three">
            <div class="container">
                <div class="row gutter-y-50">
                    <div class="col-lg-6">
                        <div class="about-three__content">
                            <div class="sec-title">
                                <h6 class="sec-title__tagline @@extraClassName">What we focus on</h6>
                                <h3 class="sec-title__title">
                                    Best solutions for <span class="sec-title__title__inner">a secure digital
                                        world</span></h3>
                            </div>
                            <p class="about-three__text about-three__text--one">In today’s fast-paced digital
                                landscape,
                                security is more than just a necessity—it’s a foundation for success. At
                                CyberShelter,
                                we’re committed to providing the best, most effective cybersecurity solutions that
                                empower businesses to thrive in a secure environment. Our innovative approach
                                ensures
                                that your data, systems, and reputation are safeguarded against both present and
                                future
                                risks. With CyberShelter, you’re not just protected; you’re prepared for a secure
                                and
                                prosperous digital future.</p>

                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="about-three__image">
                            <div class=" wow fadeInLeft animated" data-wow-duration="1500ms" data-wow-delay="00ms"
                                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInLeft;">
                                <img src="https://www.cybershelter.com/assets_web/images-2/new/securityr.png" class="img-fluid">


                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </section>

        <section class="counter-wrapper mt-5">
            <div class="counter-inner">
                <div class="container">
                    <div class="row aos-init aos-animate" data-aos="fade-left" data-aos-easing="linear"
                        data-aos-duration="1000">
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="py-4 text-center text-white">
                                <div class="py-2">
                                    <span id="count1" class="count_number">27+</span>
                                </div>
                                <div>Years of experience</div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="py-4 text-center text-white">
                                <div class="py-2">
                                    <span id="count2" class="count_number">250+</span>
                                </div>
                                <div>Clients served</div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="py-4 text-center text-white">
                                <div class="py-2">
                                    <span id="count3" class="count_number">500+</span>
                                </div>
                                <div>Projects completed</div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="py-4 text-center text-white">
                                <div class="py-2">
                                    <span id="count4" class="count_number">150+</span>
                                </div>
                                <div>Professionals</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="team-one section-space">
            <div class="container">
                <div class="team-one__top">
                    <div class="row gutter-y-30 align-items-center">
                        <div class="col-xxl-8 col-lg-7">
                            <div class="sec-title">
                                <h6 class="sec-title__tagline @@extraClassName">Our expert team</h6>
                                <h3 class="sec-title__title">Meet the team behind our <span
                                        class="sec-title__title__inner">success</span> story</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="team-one__carousel cleenhearts-owl__carousel cleenhearts-owl__carousel--with-shadow cleenhearts-owl__carousel--basic-nav owl-theme owl-carousel"
                    data-owl-options='{
            "items": 3,
            "margin": 30,
            "smartSpeed": 700,
            "loop": true,
            "autoplay": 6000,
            "nav": true,
            "dots": false,
            "navText": ["<span class=\"icon-arrow-left\"></span>", "<span class=\"icon-arrow-right\"></span>"],
            "responsive": {
                "0": {
                    "items": 1,
                    "margin": 20
                },
                "575": {
                    "items": 1,
                    "margin": 30
                },
                "768": {
                    "items": 2,
                    "margin": 30
                },
                "992": {
                    "items": 3,
                    "margin": 30
                },
                "1200": {
                    "items": 3,
                    "margin": 30
                }
            }
        }'>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/banner/032025/d7642acba5a2164a870fac01938f6382.png"
                                        alt="Illyas Kooliyankal">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Illyas Kooliyankal                                            </h4>
                                            <p class="team-single__designation">
                                                Cybersecurity Advisor &amp; Group CEO                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/banner/032025/a596533094c0ae1e0bc7c3f959a30f2c.png"
                                        alt="Mohammed Sheefar Ibrahim Kalluveettil">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Mohammed Sheefar Ibrahim Kalluveettil                                            </h4>
                                            <p class="team-single__designation">
                                                CTO &amp; Chief of staff                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/banner/032025/c0b133a3ff80c681eae48336719187c3.png"
                                        alt="Ashique Sajjad">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Ashique Sajjad                                            </h4>
                                            <p class="team-single__designation">
                                                Chief Operating Officer (COO)                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/banner/032025/7180acf54238718a1f410dcbd5d511a7.png"
                                        alt="Mohammed Nashath">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Mohammed Nashath                                            </h4>
                                            <p class="team-single__designation">
                                                Head of Cybersecurity Services                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/banner/032025/1530ae52f049a11ea62bc3f1028ef2fa.png"
                                        alt="Mohammed Meeran Shaikh">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Mohammed Meeran Shaikh                                            </h4>
                                            <p class="team-single__designation">
                                                Group CISO                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/7213a7457ca0e8f5884dccd09d5ee9d8.png"
                                        alt="Adil Mohammed">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Adil Mohammed                                            </h4>
                                            <p class="team-single__designation">
                                                Client Director - Cybersecurity Engineer                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/d6c0582b6cb57a2a3bc1cc43622c380c.png"
                                        alt="Salman Khan">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Salman Khan                                            </h4>
                                            <p class="team-single__designation">
                                                Cybersecurity Lead Engineer                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/41cb44d2f7d2cba611058c0c02ac1e4c.png"
                                        alt="Sneha Nair">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Sneha Nair                                            </h4>
                                            <p class="team-single__designation">
                                                Client Engagement Director                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/a99d47bc53d152e5ad1d13584ac4e83c.png"
                                        alt="Aswin Jowhar">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Aswin Jowhar                                            </h4>
                                            <p class="team-single__designation">
                                                Information Security Consultant                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/7935911ed353a09825c5bc80f10b6867.png"
                                        alt="Shaziya Mahamood">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Shaziya Mahamood                                            </h4>
                                            <p class="team-single__designation">
                                                Manager - Cybersecurity Services                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/d6563782c1fe21a53cd8bf5c7554b742.png"
                                        alt="Hamza Moideen">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Hamza Moideen                                            </h4>
                                            <p class="team-single__designation">
                                                Information Security Consultant                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/286958c7f39339303b95b2762825c4bb.png"
                                        alt="Kenz Abdul Razick">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Kenz Abdul Razick                                            </h4>
                                            <p class="team-single__designation">
                                                Information Security Consultant                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/dbd6d768c57c46f3b4f6a349fb3fdb5b.png"
                                        alt="Naimur Rahman">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Naimur Rahman                                            </h4>
                                            <p class="team-single__designation">
                                                Manager - Security Operations                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/841ff94c72a8629e0f5b069cec93635b.png"
                                        alt="Sufdar Husain">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Sufdar Husain                                            </h4>
                                            <p class="team-single__designation">
                                                Cybersecurity Engineer                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/bde584a522da74c429b76767f77d6a42.png"
                                        alt="Emre Rasim">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Emre Rasim                                            </h4>
                                            <p class="team-single__designation">
                                                Cybersecurity Consultant                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/080c51ba27804457bb90e1100dac584a.png"
                                        alt="Shariq Shajir">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Shariq Shajir                                            </h4>
                                            <p class="team-single__designation">
                                                Cybersecurity Engineer                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/ca792748d69d598e9ff02cd082911167.png"
                                        alt="Vaishali Ikhlaq Hussain">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Vaishali Ikhlaq Hussain                                            </h4>
                                            <p class="team-single__designation">
                                                Manager - GRC Services                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/8ca9f77ce4464d8beebbff939561c20e.png"
                                        alt="Ajmal Aboobacker ">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Ajmal Aboobacker                                             </h4>
                                            <p class="team-single__designation">
                                                Cybersecurity Engineer                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/2a11650ecd45022b4d68f2178d20e98b.png"
                                        alt="Muhammed Sadique">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Muhammed Sadique                                            </h4>
                                            <p class="team-single__designation">
                                                Finance &amp; Administration Officer                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                            <div class="item">
                            <div class="team-single">
                                <div class="team-single__image">
                                    <img src="https://www.cybershelter.com/uploads/team/032025/e895aa14b492f1272772e1b792c2659c.png"
                                        alt="Aafreen Felza faris">
                                    <div class="team-single__content">
                                        <div class="team-single__content__inner">
                                            <h4 class="team-single__name">
                                                Aafreen Felza faris                                            </h4>
                                            <p class="team-single__designation">
                                                Finance &amp; Accounts Officer                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                    </div>
            </div>
        </section>



        <section class="help-donate-one section-space-top">

            <div class="container">
                <div class="sec-title">
                    <h6 class="sec-title__tagline sec-title__tagline--center">OUR WORLD-CLASS CYBER SECURITY
                        OFFERINGS</h6>
                </div>
            </div>
            <div class="help-donate-one__slide">
                <span class="help-donate-one__text help-donate-one__text--one">Data security , </span>
                <span class="help-donate-one__text help-donate-one__text--two">Network protection , </span>

                <span class="help-donate-one__text help-donate-one__text--one">Endpoint security , </span>
                <span class="help-donate-one__text help-donate-one__text--two">Cloud security , </span>
                <span class="help-donate-one__text help-donate-one__text--two">Risk assessment , </span>
                <span class="help-donate-one__text help-donate-one__text--one">Incident response , </span>
                <span class="help-donate-one__text help-donate-one__text--two">Compliance , </span>
            </div>
        </section>

        <section class="donations-one donations-carousel section-space-bottom">
            <div class="container">
                <div class="donations-one__carousel cleenhearts-owl__carousel cleenhearts-owl__carousel--basic-nav owl-theme owl-carousel owl-loaded owl-drag"
                    data-owl-options="{
    &quot;items&quot;: 3,
    &quot;margin&quot;: 30,
    &quot;smartSpeed&quot;: 700,
    &quot;loop&quot;:true,
    &quot;autoplay&quot;: 6000,
    &quot;nav&quot;:true,
    &quot;dots&quot;:false,
    &quot;navText&quot;: [&quot;<span class=\&quot;icon-arrow-left\&quot;></span>&quot;,&quot;<span class=\&quot;icon-arrow-right\&quot;></span>&quot;],
    &quot;responsive&quot;:{
        &quot;0&quot;:{
            &quot;items&quot;: 1,
            &quot;margin&quot;: 20
        },
        &quot;576&quot;:{
            &quot;items&quot;: 1,
            &quot;margin&quot;: 30
        },
        &quot;768&quot;:{
            &quot;items&quot;: 2,
            &quot;margin&quot;: 30
        },
        &quot;992&quot;:{
            &quot;items&quot;: 2,
            &quot;margin&quot;: 30
        },
        &quot;1200&quot;:{
            &quot;items&quot;: 3,
            &quot;margin&quot;: 30
        }
    }
    }">

                    <div class="owl-stage-outer">
                        <div class="owl-stage"
                            style="transform: translate3d(-2000px, 0px, 0px); transition: 0.7s; width: 4800px;">
                            <div class="owl-item active" style="width: 370px; margin-right: 30px;">
                                <div class="item wow fadeInUp animated" data-wow-duration="1500ms"
                                    data-wow-delay="00ms"
                                    style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                                    <div class="donation-card @@extraClassName">
                                        <div class="donation-card__bg"></div>
                                        <a href="#" class="donation-card__image">
                                            <img src="https://www.cybershelter.com/assets_web/images-2/new/cloud-security.webp"
                                                alt="Quisque dictum eget accumsan dignissim. Quisque">
                                        </a>
                                        <div class="donation-card__content">
                                            <h3 class="donation-card__title"><a href="#">Cloud security</a></h3>
                                            <p>Trust CyberShelter's innovative cloud security solutions and
                                                experienced experts to safeguard your cloud journey, as security
                                                controls and assurance are vital decision-making factors.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="owl-item active" style="width: 370px; margin-right: 30px;">
                                <div class="item wow fadeInUp animated" data-wow-duration="1500ms"
                                    data-wow-delay="00ms"
                                    style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                                    <div class="donation-card @@extraClassName">
                                        <div class="donation-card__bg"></div>
                                        <a href="#" class="donation-card__image">
                                            <img src="https://www.cybershelter.com/assets_web/images-2/new/threat-2.webp"
                                                alt="Quisque dictum eget accumsan dignissim. Quisque">
                                        </a>
                                        <div class="donation-card__content">
                                            <h3 class="donation-card__title"><a href="#">Threat intelligence</a>
                                            </h3>
                                            <p>Effective threat intelligence equips organizations to take proactive
                                                action, addressing cyber threats before they cause harm, and
                                                ensuring responsive strategies for enhanced protection.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="owl-item active" style="width: 370px; margin-right: 30px;">
                                <div class="item wow fadeInUp animated" data-wow-duration="1500ms"
                                    data-wow-delay="00ms"
                                    style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                                    <div class="donation-card @@extraClassName">
                                        <div class="donation-card__bg"></div>
                                        <a href="#" class="donation-card__image">
                                            <img src="https://www.cybershelter.com/assets_web/images-2/new/professional-does-ai-systems-checkup-2.jpg"
                                                alt="Quisque dictum eget accumsan dignissim. Quisque">
                                        </a>
                                        <div class="donation-card__content">
                                            <h3 class="donation-card__title"><a href="#">Human security</a></h3>
                                            <p>Organizations benefit from combining technology and trained staff to
                                                develop comprehensive strategies that address information security
                                                and foster cyber-aware cultures within their teams.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="item wow fadeInUp animated" data-wow-duration="1500ms"
                                    data-wow-delay="00ms"
                                    style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                                    <div class="donation-card @@extraClassName">
                                        <div class="donation-card__bg"></div>
                                        <a href="#" class="donation-card__image">
                                            <img src="https://www.cybershelter.com/assets_web/images-2/new/close-up-programmer-typing-laptop.webp"
                                                alt="Quisque dictum eget accumsan dignissim. Quisque">
                                        </a>
                                        <div class="donation-card__content">
                                            <h3 class="donation-card__title"><a href="#">Cyber defense</a></h3>
                                            <p>CyberShelter's Managed SOC ensures complete protection by offering
                                                continuous monitoring, incident response, and expert support to
                                                alleviate security management challenges for businesses.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="item wow fadeInUp animated" data-wow-duration="1500ms"
                                    data-wow-delay="00ms"
                                    style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                                    <div class="donation-card @@extraClassName">
                                        <div class="donation-card__bg"></div>
                                        <a href="#" class="donation-card__image">
                                            <img src="https://www.cybershelter.com/assets_web/images-2/new/ransome-2.webp"
                                                alt="Quisque dictum eget accumsan dignissim. Quisque">
                                        </a>
                                        <div class="donation-card__content">
                                            <h3 class="donation-card__title"><a href="#">Ransomware management</a>
                                            </h3>
                                            <p>Organizations leverage CyberShelter's ransomware mitigation
                                                strategies to stay prepared, ensuring that systems are equipped to
                                                withstand and recover from disruptive ransomware attacks.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="item wow fadeInUp animated" data-wow-duration="1500ms"
                                    data-wow-delay="00ms"
                                    style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                                    <div class="donation-card @@extraClassName">
                                        <div class="donation-card__bg"></div>
                                        <a href="#" class="donation-card__image">
                                            <img src="https://www.cybershelter.com/assets_web/images-2/new/hacker-cracking-binary-code-data-security-2.jpg"
                                                alt="Quisque dictum eget accumsan dignissim. Quisque">
                                        </a>
                                        <div class="donation-card__content">
                                            <h3 class="donation-card__title"><a href="#">Security finetuning</a>
                                            </h3>
                                            <p>Our security finetuning services optimize existing systems, ensuring
                                                advanced protection, efficient functionality, and resilience against
                                                emerging cyber threats and vulnerabilities.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="item wow fadeInUp animated" data-wow-duration="1500ms"
                                    data-wow-delay="00ms"
                                    style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                                    <div class="donation-card @@extraClassName">
                                        <div class="donation-card__bg"></div>
                                        <a href="#" class="donation-card__image">
                                            <img src="https://www.cybershelter.com/assets_web/images-2/new/close-up-programmer-typing-keyboard-2.webp"
                                                alt="Quisque dictum eget accumsan dignissim. Quisque">
                                        </a>
                                        <div class="donation-card__content">
                                            <h3 class="donation-card__title"><a href="#">Continuous pentesting</a>
                                            </h3>
                                            <p>Our state-of-the-art Continuous PenTesting ensures your external
                                                systems are routinely assessed for vulnerabilities, providing
                                                unmatched security and confidence in your defenses.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="item wow fadeInUp animated" data-wow-duration="1500ms"
                                    data-wow-delay="00ms"
                                    style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                                    <div class="donation-card @@extraClassName">
                                        <div class="donation-card__bg"></div>
                                        <a href="#" class="donation-card__image">
                                            <img src="https://www.cybershelter.com/assets_web/images-2/new/scene-with-business-person-working-futuristic-office-job-2.webp"
                                                alt="Quisque dictum eget accumsan dignissim. Quisque">
                                        </a>
                                        <div class="donation-card__content">
                                            <h3 class="donation-card__title"><a href="#">Supply chain security</a>
                                            </h3>
                                            <p>CyberShelter offers comprehensive support for assessing and managing
                                                risks within vendor and third-party relationships to ensure complete
                                                security for your organization.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="item wow fadeInUp animated" data-wow-duration="1500ms"
                                    data-wow-delay="00ms"
                                    style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                                    <div class="donation-card @@extraClassName">
                                        <div class="donation-card__bg"></div>
                                        <a href="#" class="donation-card__image">
                                            <img src="https://www.cybershelter.com/assets_web/images-2/new/professional-does-ai-systems-checkup-2.jpg"
                                                alt="Quisque dictum eget accumsan dignissim. Quisque">
                                        </a>
                                        <div class="donation-card__content">
                                            <h3 class="donation-card__title"><a href="#">AD security</a></h3>
                                            <p>Active Directory vulnerabilities demand expert focus. CyberShelter
                                                specializes in AD security strategies that secure authentication and
                                                account management systems across organizations.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="owl-dots disabled"></div>
                </div>

            </div>
        </section>








        <section class="blog-three section-space">
            <div class="container">
                <div class="sec-title">
                    <!--<h6 class="sec-title__tagline sec-title__tagline--center">Latest cybersecurity news</h6>-->
                    <h3 class="sec-title__title">Latest news &amp; articles from secure <span
                            class="sec-title__title__inner">reading</span></h3>
                </div>
                <div class="row gutter-y-30">
                    <div class="col-lg-6">
                        <div class="blog-card-three">
                            <div class="blog-card-three__bg"></div>
                            <a href="#" class="blog-card-three__image">
                                <img src="https://www.cybershelter.com/assets_web/images-2/news-1-2.webp"
                                    alt="Cybersecurity Threats and Solutions">
                                <div class="blog-card-three__date"><span>03</span> <span>SEP</span></div>
                            </a>
                            <div class="blog-card-three__content">
                                <h3 class="blog-card-three__title"><a href="#">Cybersecurity threats and solutions
                                        in 2024</a></h3>
                                <a href="#" class="cleenhearts-btn">
                                    <div class="cleenhearts-btn__icon-box">
                                        <div class="cleenhearts-btn__icon-box__inner"><span
                                                class="icon-duble-arrow"></span></div>
                                    </div>
                                    <a href="https://securereading.com/"><span class="cleenhearts-btn__text hover-color-change"
                                            style="color: #fff !important;">Read more</span></a>

                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="blog-card-three">
                            <div class="blog-card-three__bg"></div>
                            <a href="#" class="blog-card-three__image">
                                <img src="https://www.cybershelter.com/assets_web/images-2/240_F_480884814_busW8r7P4VroKSGjCupiyBekzGT61kVF.jpg"
                                    alt="Latest Cybersecurity Breaches and How to Protect">
                                <div class="blog-card-three__date"><span>09</span> <span>OCT</span></div>
                            </a>
                            <div class="blog-card-three__content">
                                <h3 class="blog-card-three__title"><a href="#">Latest cybersecurity breaches and how
                                        to protect</a></h3>
                                <a href="#" class="cleenhearts-btn">
                                    <div class="cleenhearts-btn__icon-box">
                                        <div class="cleenhearts-btn__icon-box__inner"><span
                                                class="icon-duble-arrow"></span></div>
                                    </div>
                                    <a href="https://securereading.com/"><span class="cleenhearts-btn__text hover-color-change"
                                            style="color: #fff !important;">Read more</span></a>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </section>




        <section class="events-gallery">
            <div class="events-gallery__carousel cleenhearts-owl__carousel cleenhearts-owl__carousel--basic-nav owl-carousel"
                data-owl-options='{
        "loop": true,
        "items": 5,
        "autoplay": true,
        "autoplayTimeout": 7000,
        "smartSpeed": 1000,
        "nav": false,
        "navText": ["<span class=\"icon-left-arrow\"></span>", "<span class=\"icon-right-arrow\"></span>"],
        "dots": true,
        "margin": 0,
        "responsive": {
            "0": {
                "items": 1
            },
            "768": {
                "items": 2
            },
            "992": {
                "items": 3
            },
            "1200": {
                "items": 4
            },
            "1500": {
                "items": 5
            }
        }
    }'>
                                    <div class="item">
                        <div class="events-gallery__item">
                            <div class="events-gallery__image">
                                <img src="https://www.cybershelter.com/uploads/insta_feed/022025/c86e5ee1f780771e8de5cf1c4178b4ce.jpg" alt="Explore Instagram"
                                    class="events-gallery__image__img">
                                <a href="https://www.instagram.com/cyber_shelter/?hl=en" class="events-gallery__link">
                                    <img src="https://www.cybershelter.com/assets_web/images-2/954290_2227.svg" alt="instagram">
                                    <span>Explore instagram</span>
                                </a>
                            </div>
                        </div>
                    </div>
                                    <div class="item">
                        <div class="events-gallery__item">
                            <div class="events-gallery__image">
                                <img src="https://www.cybershelter.com/uploads/insta_feed/022025/c86e5ee1f780771e8de5cf1c4178b4ce.jpg" alt="Explore Instagram"
                                    class="events-gallery__image__img">
                                <a href="https://www.instagram.com/cyber_shelter/?hl=en" class="events-gallery__link">
                                    <img src="https://www.cybershelter.com/assets_web/images-2/954290_2227.svg" alt="instagram">
                                    <span>Explore instagram</span>
                                </a>
                            </div>
                        </div>
                    </div>
                                    <div class="item">
                        <div class="events-gallery__item">
                            <div class="events-gallery__image">
                                <img src="https://www.cybershelter.com/uploads/insta_feed/022025/e70c409bd174e73882e173e7be3f519a.jpg" alt="Explore Instagram"
                                    class="events-gallery__image__img">
                                <a href="https://www.instagram.com/cyber_shelter/?hl=en" class="events-gallery__link">
                                    <img src="https://www.cybershelter.com/assets_web/images-2/954290_2227.svg" alt="instagram">
                                    <span>Explore instagram</span>
                                </a>
                            </div>
                        </div>
                    </div>
                                    <div class="item">
                        <div class="events-gallery__item">
                            <div class="events-gallery__image">
                                <img src="https://www.cybershelter.com/uploads/insta_feed/022025/e70c409bd174e73882e173e7be3f519a.jpg" alt="Explore Instagram"
                                    class="events-gallery__image__img">
                                <a href="https://www.instagram.com/cyber_shelter/?hl=en" class="events-gallery__link">
                                    <img src="https://www.cybershelter.com/assets_web/images-2/954290_2227.svg" alt="instagram">
                                    <span>Explore instagram</span>
                                </a>
                            </div>
                        </div>
                    </div>
                                    <div class="item">
                        <div class="events-gallery__item">
                            <div class="events-gallery__image">
                                <img src="https://www.cybershelter.com/uploads/insta_feed/022025/d219fd42380c9ac942cccf0988105376.jpg" alt="Explore Instagram"
                                    class="events-gallery__image__img">
                                <a href="https://www.instagram.com/johndoe/" class="events-gallery__link">
                                    <img src="https://www.cybershelter.com/assets_web/images-2/954290_2227.svg" alt="instagram">
                                    <span>Explore instagram</span>
                                </a>
                            </div>
                        </div>
                    </div>
                                    <div class="item">
                        <div class="events-gallery__item">
                            <div class="events-gallery__image">
                                <img src="https://www.cybershelter.com/uploads/insta_feed/022025/d219fd42380c9ac942cccf0988105376.jpg" alt="Explore Instagram"
                                    class="events-gallery__image__img">
                                <a href="https://www.instagram.com/johndoe/" class="events-gallery__link">
                                    <img src="https://www.cybershelter.com/assets_web/images-2/954290_2227.svg" alt="instagram">
                                    <span>Explore instagram</span>
                                </a>
                            </div>
                        </div>
                    </div>
                                    <div class="item">
                        <div class="events-gallery__item">
                            <div class="events-gallery__image">
                                <img src="https://www.cybershelter.com/uploads/insta_feed/022025/6662c0c7adfdba6a285f3b116ea005b3.jpg" alt="Explore Instagram"
                                    class="events-gallery__image__img">
                                <a href="http://test.com/" class="events-gallery__link">
                                    <img src="https://www.cybershelter.com/assets_web/images-2/954290_2227.svg" alt="instagram">
                                    <span>Explore instagram</span>
                                </a>
                            </div>
                        </div>
                    </div>
                                    <div class="item">
                        <div class="events-gallery__item">
                            <div class="events-gallery__image">
                                <img src="https://www.cybershelter.com/uploads/insta_feed/022025/6662c0c7adfdba6a285f3b116ea005b3.jpg" alt="Explore Instagram"
                                    class="events-gallery__image__img">
                                <a href="http://test.com/" class="events-gallery__link">
                                    <img src="https://www.cybershelter.com/assets_web/images-2/954290_2227.svg" alt="instagram">
                                    <span>Explore instagram</span>
                                </a>
                            </div>
                        </div>
                    </div>
                            </div>
        </section>







        <footer class="footer-three footer-two main-footer">
            <div class="main-footer__top">
                <div class="container">
                    <div class="row gutter-y-30">
                        <div class="col-md-12 col-xl-3 wow fadeInUp" data-wow-duration="1500ms"
                            data-wow-delay="00ms">
                            <div class="about-info-two__logo">
                                <img src="https://www.cybershelter.com/assets_web/images-2/new/CyberShelter-optimized.png" alt="logo-dark"
                                    width="200">
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-5 wow fadeInUp" data-wow-duration="1500ms"
                            data-wow-delay="200ms">
                            <div class="footer-widget footer-widget--contact">
                                <h2 class="footer-widget__title">Get in touch!</h2>
                                <ul class="list-unstyled footer-widget__info">
                                    <li>
                                        <span class="icon-location"></span>
                                        <address>CyberShelter Infosec Solutions
                                            110, ASB Tower, Dubai Silicon Oasis, Dubai, UAE</address>
                                    </li>
                                    <li>
                                        <span class="icon-phone"></span><a href="tel: +971501146930">
                                            +971501146930</a>
                                    </li>
                                    <li>
                                        <span class="icon-envelope"></span><a href="/cdn-cgi/l/email-protection#cca5a2aaa38cafb5aea9bebfa4a9a0b8a9bee2afa3a1"><span class="__cf_email__" data-cfemail="8be2e5ede4cbe8f2e9eef9f8e3eee7ffeef9a5e8e4e6">[email&#160;protected]</span></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-3 wow fadeInUp" data-wow-duration="1500ms" data-wow-delay="400ms">
                            <div class="footer-widget footer-widget--links">
                                <h2 class="footer-widget__title">Quick links</h2>
                                <ul class="list-unstyled footer-widget__links">
                                    <li><a href="https://www.cybershelter.com/founded">About us</a></li>
                                    <li><a href="#">Professional services</a></li>
                                    <li><a href="#">Privacy policy</a></li>
                                    <li><a href="#">Terms and conditions</a></li>
                                    <li><a href="https://wa.me/+971501146930">Contact us</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-4 col-xl-3 wow fadeInUp" data-wow-duration="1500ms"
                            data-wow-delay="600ms">
                            <h2 class="footer-widget__title hide-social">Social media</h2>
                            <div class="about-info-two__social social-link-two">
                                <a href="https://www.facebook.com/share/15n3wtEqRh/?mibextid=wwXIfr">
                                    <i class="fab fa-facebook-f" aria-hidden="true"></i>
                                    <span class="sr-only">Facebook</span>
                                </a>
                                <a href="https://www.instagram.com/cyber_shelter?igsh=Z3Q0N2FvZTRrZHRq">
                                    <i class="fab fa-instagram" aria-hidden="true"></i>
                                    <span class="sr-only">Instagram</span>
                                </a>

                                <a href="https://www.linkedin.com/company/cybershelter/" aria-hidden="true">
                                    <i class="fab fa-linkedin-in"></i>
                                    <span class="sr-only">Linkedin</span>
                                </a>
                                <a href="https://x.com/cybershelter?s=11&t=C8B_Vn1maNKRWlTuPdT1hg"> <i
                                        class="fab fa-twitter" aria-hidden="true"></i> <span
                                        class="sr-only">Twitter</span> </a>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-three__bottom">
                <div class="container">
                    <div class="footer-three__bottom__inner">
                        <p class="footer-three__copyright">
                            &copy; Copyright 2025 Cybershelter all rights reserved.
                        </p>
                    </div>
                </div>
            </div>
        </footer>


    </div>

    <div class="mobile-nav__wrapper">
        <div class="mobile-nav__overlay mobile-nav__toggler"></div>

        <div class="mobile-nav__content">
            <span class="mobile-nav__close mobile-nav__toggler"><i class="fa fa-times"></i></span>

            <div class="logo-box">
                <a href="index.html" aria-label="logo image"><img
                        src="https://www.cybershelter.com/assets_web/images/logo-light.png" width="155" alt="" /></a>
            </div>

            <div class="mobile-nav__container"></div>


            <ul class="mobile-nav__contact list-unstyled">
                <li>
                    <i class="fa fa-envelope"></i>
                    <a href="/cdn-cgi/l/email-protection#c1a8afa7ae81a2b8a3a4b3b2a9a4adb5a4b3efa2aeac"><span class="__cf_email__" data-cfemail="1d74737b725d7e647f786f6e75787169786f337e7270">[email&#160;protected]</span></a>
                </li>
                <li>
                    <i class="fa fa-phone-alt"></i>
                    <a href="tel:+971501146930"> +971501146930</a>
                </li>
            </ul>
            <div class="mobile-nav__social">
                <a href="https://www.facebook.com/share/15n3wtEqRh/?mibextid=wwXIfr">
                    <i class="fab fa-facebook-f" aria-hidden="true"></i>
                    <span class="sr-only">Facebook</span>
                </a>
                <a href="https://x.com/cybershelter?s=11&t=C8B_Vn1maNKRWlTuPdT1hg">
                    <i class="fab fa-twitter" aria-hidden="true"></i>
                    <span class="sr-only">Twitter</span>
                </a>
                <a href="https://www.linkedin.com/company/cybershelter/" aria-hidden="true">
                    <i class="fab fa-linkedin-in"></i>
                    <span class="sr-only">Linkedin</span>
                </a>
                <a href="https://youtube.com/" aria-hidden="true">
                    <i class="fab fa-youtube"></i>
                    <span class="sr-only">Youtube</span>
                </a>
            </div>
        </div>

    </div>

    <div class="floating-icons">
        <a href="https://wa.me/1234567890" target="_blank" class="whatsapp-icon">
            <i class="fab fa-whatsapp"></i>
        </a>
        <a href="tel:+1234567890" class="call-icon">
            <i class="fas fa-phone-alt"></i>
        </a>
    </div>










    <style>
        .header-nav .nav>li .mega-menu {
            background: rgba(17, 29, 32, 0.9);
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(5px) !important;
            -webkit-backdrop-filter: blur(5px) !important;
            border: 1px solid rgba(17, 29, 32, 0.5);
            display: flex;
            left: 0px;
            list-style: none;
            opacity: 2;
            position: absolute;
            right: 0px;
            visibility: hidden;
            width: 100%;
            margin-top: 20px;
            z-index: 9;
            padding: 30px 10px;
            gap: 50px;
        }

        @media (max-width: 600px) {
            #dial-main {
                margin-top: 0 !important;
            }
        }
    </style>



    <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="https://www.cybershelter.com/assets_web/vendors/jquery/jquery-3.7.0.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/bootstrap/js/bootstrap.bundle.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/bootstrap-select/bootstrap-select.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/jarallax/jarallax.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/jquery-ui/jquery-ui.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/jquery-appear/jquery.appear.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/jquery-circle-progress/jquery.circle-progress.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/jquery-magnific-popup/jquery.magnific-popup.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/jquery-validate/jquery.validate.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/nouislider/nouislider.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/tiny-slider/tiny-slider.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/wnumb/wNumb.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/swiper/js/swiper-bundle.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/owl-carousel/js/owl.carousel.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/wow/wow.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/imagesloaded/imagesloaded.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/isotope/isotope.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/countdown/countdown.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/jquery-circleType/jquery.circleType.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/vendors/jquery-lettering/jquery.lettering.min.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.1/dist/aos.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/js/main.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>
    <script src="https://www.cybershelter.com/assets_web/js/slider.js" type="b504f74f83a97a3fca55ee88-text/javascript"></script>







    <script type="b504f74f83a97a3fca55ee88-text/javascript">
        AOS.init({
            disable: function() {
                return window.innerWidth < 768;
            },
            once: true,
            duration: 600
        });
    </script>

<script src="/cdn-cgi/scripts/7d0fa10a/cloudflare-static/rocket-loader.min.js" data-cf-settings="b504f74f83a97a3fca55ee88-|49" defer></script><script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'94e8214b1e9317aa',t:'MTc0OTcxODUzNS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>

</html>