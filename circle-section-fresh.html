<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Circle Section - Exact Replica</title>
    <style>
        /* Remove default margins and padding */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            font-family: "Titillium Web", serif;
        }

        .cs-banner-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        /* Background video styling */
        .cs-background-video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 0;
        }

        /* Video overlay for darkening effect */
        .cs-video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.7));
            z-index: 1;
        }

        /* Business background effect - now applied over video */
        .banner-container::before {
            content: '';
            position: absolute;
            inset: 0;
            background: radial-gradient(circle at 30% 40%, rgba(100, 116, 139, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 70% 60%, rgba(71, 85, 105, 0.2) 0%, transparent 50%);
            z-index: 2;
        }

        .cs-main-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .cs-circular-component {
            position: relative;
            width: 85%; /* 85% of screen width for better mobile experience */
            max-width: 750px; /* Reduced from 850px */
            margin: 0 auto;
            aspect-ratio: 1/1;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cs-circular-component:hover {
            filter: brightness(1.3) contrast(1.1);
            transform: scale(1.02);
        }

        .cs-central-content {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 5;
            pointer-events: none; /* Allow clicks to pass through to SVG */
        }

        .cs-content-inner {
            text-align: center;
            transition: opacity 0.4s ease-in-out;
            pointer-events: auto; /* Re-enable pointer events for content */
        }

        .content-inner.transitioning {
            opacity: 0;
        }

        .cs-main-title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
            line-height: 1;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .cs-subtitle-line {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            line-height: 1;
            margin-bottom: 0.25rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .cs-subtitle-line:last-of-type {
            margin-bottom: 3rem;
        }

        .cs-cta-button {
            background: #111d20;
            backdrop-filter: blur(10px);
            color: white;
            padding: 0.75rem 1.75rem;
            border: none;
            border-radius: 25px 8px 25px 8px; /* Leaf-like asymmetric rounded corners */
            font-weight: 600;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 20px #111d20;
            position: relative;
            overflow: hidden;
        }

        .cs-cta-button::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: #1a2a2e;
            border-radius: 27px 10px 27px 10px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .cs-cta-button:hover {
            background: #111d20;
            transform: translateY(-2px) rotate(1deg); /* Slight rotation for organic feel */
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        .cs-cta-button:hover::before {
            opacity: 1;
        }

        .cs-svg-container {
            position: absolute;
            inset: 0;
            z-index: 1;
        }

        .cs-circular-svg {
            width: 100%;
            height: 100%;
        }

        /* Circle styling */
        .cs-circle-border {
            fill: none;
            stroke: rgba(255, 255, 255, 0.4);
            stroke-width: 2;
        }

        .cs-circle-border-dots {
            fill: none;
            stroke: rgba(255, 255, 255, 0.4);
            stroke-width: 2;
            stroke-dasharray: 2 6;
            stroke-linecap: round;
        }

        .cs-progress-ring {
            fill: none;
            stroke: #b9d989;
            stroke-width: 3;
            stroke-linecap: round;
            transition: stroke-dasharray 1s ease-out;
        }

        /* Mobile progress ring width increase */
        @media (max-width: 480px) {
            .cs-progress-ring {
                stroke-width: 5; /* Increased from 3 to 5 for mobile */
            }
        }

        .tick-mark {
            stroke: rgba(255, 255, 255, 0.8);
            stroke-width: 1;
            stroke-linecap: round;
        }

        .tick-mark-bold {
            stroke: rgba(255, 255, 255, 1);
            stroke-width: 2;
            stroke-linecap: round;
        }

        .tick-mark-small {
            stroke: rgba(255, 255, 255, 0.4);
            stroke-width: 1;
            stroke-linecap: round;
        }

        /* Animations */
        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.5; }
        }

        @keyframes ping {
            0% { opacity: 0.3; transform: scale(1); }
            75% { opacity: 0; transform: scale(1.5); }
            100% { opacity: 0; transform: scale(1.5); }
        }

        .pulse-slow {
            animation: pulse 3s ease-in-out infinite;
        }

        .ping {
            animation: ping 2s ease-in-out infinite;
        }





        /* Enhanced Responsive Design */
        @media (max-width: 480px) {
            .cs-main-title, .cs-subtitle-line {
                font-size: 1.5rem; /* Increased from 1.25rem */
                line-height: 1.3;
            }
            .cs-circular-component {
                width: 95%; /* Slightly reduced for better balance */
                max-width: none; /* Remove max-width constraint for mobile */
                min-width: 400px; /* Slightly reduced minimum size */
                margin: 0 auto; /* Ensure proper centering */
                display: block; /* Ensure block display for centering */
            }
            .cs-cta-button {
                padding: 0.6rem 1.2rem; /* Slightly larger button */
                font-size: 0.85rem; /* Increased button text size */
                gap: 0.4rem;
            }
            .cs-main-container {
                padding: 0.5rem; /* Increased padding for better balance */
                display: flex;
                justify-content: center;
                align-items: center;
            }

            /* Larger points for mobile */
            .small-point {
                r: 3.5; /* Further increased for better visibility */
            }
            .medium-point {
                r: 7; /* Further increased for better touch targets */
            }
            .large-point {
                r: 10; /* Further increased for better interaction */
            }
        }

        @media (min-width: 481px) and (max-width: 640px) {
            .main-title, .subtitle-line {
                font-size: 1.75rem; /* Increased for small tablets */
            }
            .circular-component {
                width: 100%; /* Maximum width for small tablets */
                max-width: 650px; /* Increased max-width */
                min-width: 450px; /* Added minimum width */
            }
            .cta-button {
                padding: 0.6rem 1.4rem; /* Larger button */
                font-size: 0.9rem; /* Increased button text */
                gap: 0.4rem;
            }
            .main-container {
                padding: 0.5rem; /* Reduced padding */
            }
        }

        @media (min-width: 641px) and (max-width: 768px) {
            .main-title, .subtitle-line {
                font-size: 2rem;
            }
            .circular-component {
                width: 80%; /* Slightly less on larger screens */
                max-width: 550px;
            }
            .cta-button {
                padding: 0.625rem 1.5rem;
                font-size: 0.875rem;
                gap: 0.45rem;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .main-title, .subtitle-line {
                font-size: 2.5rem;
            }
            .circular-component {
                width: 75%; /* Reduce width percentage for larger screens */
                max-width: 650px;
            }
            .cta-button {
                padding: 0.65rem 1.5rem;
                font-size: 0.9rem;
                gap: 0.45rem;
            }
        }

        @media (min-width: 1025px) and (max-width: 1280px) {
            .main-title, .subtitle-line {
                font-size: 2.75rem;
            }
            .circular-component {
                width: 70%; /* Further reduce for desktop */
                max-width: 700px;
            }
            .cta-button {
                padding: 0.7rem 1.6rem;
                font-size: 0.925rem;
                gap: 0.475rem;
            }
        }

        @media (min-width: 1281px) and (max-width: 1536px) {
            .main-title, .subtitle-line {
                font-size: 3rem;
            }
            .circular-component {
                width: 65%; /* Balanced size for large screens */
                max-width: 750px;
            }
            .cta-button {
                padding: 0.75rem 1.75rem;
                font-size: 0.95rem;
                gap: 0.5rem;
            }
        }

        @media (min-width: 1537px) {
            .main-title, .subtitle-line {
                font-size: 3.5rem;
            }
            .circular-component {
                width: 60%; /* Reasonable size for very large screens */
                max-width: 800px;
            }
            .cta-button {
                padding: 0.8rem 1.85rem;
                font-size: 1rem;
                gap: 0.525rem;
            }
        }

        /* Landscape and touch optimizations */
        @media (max-height: 600px) and (orientation: landscape) {
            .banner-container {
                min-height: 100vh;
                padding: 0.5rem 0; /* Reduced padding */
            }
            .main-title, .subtitle-line {
                font-size: 1.25rem !important;
            }
            .circular-component {
                width: 85% !important; /* Increased for landscape mobile */
                max-width: 500px !important; /* Increased max-width */
            }
            .subtitle-line:last-of-type {
                margin-bottom: 1.5rem;
            }
            .cta-button {
                padding: 0.5rem 1rem !important;
                font-size: 0.75rem !important;
                gap: 0.375rem !important;
            }
            .main-container {
                padding: 0.5rem !important; /* Reduced padding for landscape */
            }
        }

        @media (hover: none) and (pointer: coarse) {
            .cta-button:hover {
                transform: none;
            }
        }
    </style>
</head>
<body>
    <div class="cs-banner-container">
        <!-- Background Video -->
        <video class="cs-background-video" autoplay muted loop playsinline>
            <source src="https://trogon.info/website/cs-new/images-2/new/video-2.mp4" type="video/mp4">
            <!-- Fallback for browsers that don't support video -->
            Your browser does not support the video tag.
        </video>

        <!-- Video overlay for darkening effect -->
        <div class="cs-video-overlay"></div>

        <div class="cs-main-container">
            <div class="cs-circular-component">
                <!-- Central Content -->
                <div class="cs-central-content">
                    <div class="cs-content-inner" id="csContentInner">
                        <h1 class="cs-main-title" id="csMainTitle">Aligning</h1>
                        <h2 class="cs-subtitle-line" id="csSubtitle1">Cybersecurity with</h2>
                        <h2 class="cs-subtitle-line" id="csSubtitle2">Business Goals</h2>
                        <button class="cs-cta-button" id="csCtaButton">
                            <span id="csButtonText">Know More</span>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"></polyline>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- SVG Circle -->
                <div class="cs-svg-container">
                    <svg class="cs-circular-svg" viewBox="0 0 690 690">
                        <!-- Outer circle border removed - using only vertical tick marks -->

                        <!-- Inner circle border removed - using individual dots instead -->

                        <!-- Progress ring on inner circle -->
                        <circle id="csProgressRing" cx="345" cy="345" r="276" class="cs-progress-ring"
                                transform="rotate(-90 345 345)" stroke-dasharray="0 1734"/>


                        <!-- Interactive elements container -->
                        <g id="csPointsContainer"></g>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Content data - 16 sections for all interactive points (4 large + 12 medium dots)
        const contentData = [
            // Cardinal points (large dots)
            {
                title: "Advanced",
                subtitle1: "Threat Detection",
                subtitle2: "& Prevention",
                buttonText: "Explore Solutions",
                link: "#threat-detection"
            },
            {
                title: "Network",
                subtitle1: "Security",
                subtitle2: "Monitoring",
                buttonText: "View Details",
                link: "#network-security"
            },
            {
                title: "Endpoint",
                subtitle1: "Protection",
                subtitle2: "Solutions",
                buttonText: "Learn More",
                link: "#endpoint-protection"
            },
            {
                title: "Cloud",
                subtitle1: "Security",
                subtitle2: "Services",
                buttonText: "Discover More",
                link: "#cloud-security"
            },
            {
                title: "Strategic",
                subtitle1: "Security",
                subtitle2: "Consulting",
                buttonText: "Get Started",
                link: "#security-consulting"
            },
            {
                title: "Compliance",
                subtitle1: "Management",
                subtitle2: "& Auditing",
                buttonText: "View Services",
                link: "#compliance"
            },
            {
                title: "Security",
                subtitle1: "Training",
                subtitle2: "Programs",
                buttonText: "Enroll Now",
                link: "#training"
            },
            {
                title: "Vulnerability",
                subtitle1: "Assessment",
                subtitle2: "& Testing",
                buttonText: "Schedule Test",
                link: "#vulnerability"
            },
            {
                title: "Rapid",
                subtitle1: "Incident Response",
                subtitle2: "& Recovery",
                buttonText: "Emergency Support",
                link: "#incident-response"
            },
            {
                title: "Digital",
                subtitle1: "Forensics",
                subtitle2: "Investigation",
                buttonText: "Contact Team",
                link: "#forensics"
            },
            {
                title: "Business",
                subtitle1: "Continuity",
                subtitle2: "Planning",
                buttonText: "Plan Now",
                link: "#continuity"
            },
            {
                title: "Disaster",
                subtitle1: "Recovery",
                subtitle2: "Solutions",
                buttonText: "Get Protected",
                link: "#disaster-recovery"
            },
            {
                title: "Comprehensive",
                subtitle1: "Risk Assessment",
                subtitle2: "& Management",
                buttonText: "Learn More",
                link: "#risk-assessment"
            },
            {
                title: "Security",
                subtitle1: "Architecture",
                subtitle2: "Design",
                buttonText: "Consult Expert",
                link: "#architecture"
            },
            {
                title: "Identity",
                subtitle1: "Access",
                subtitle2: "Management",
                buttonText: "Secure Access",
                link: "#identity"
            },
            {
                title: "Data",
                subtitle1: "Protection",
                subtitle2: "& Privacy",
                buttonText: "Protect Data",
                link: "#data-protection"
            }
        ];

        // State management - EXACT COPY FROM ORIGINAL
        let activePoint = 0;
        let previousPoint = -1; // Track the previous point for marking
        let isAutoPlaying = true;
        let isTransitioning = false;
        let autoPlayInterval;

        // DOM elements - EXACT COPY FROM ORIGINAL
        const contentInner = document.getElementById('contentInner');
        const mainTitle = document.getElementById('mainTitle');
        const subtitle1 = document.getElementById('subtitle1');
        const subtitle2 = document.getElementById('subtitle2');
        const buttonText = document.getElementById('buttonText');
        const ctaButton = document.getElementById('ctaButton');
        const progressRing = document.getElementById('progressRing');
        const pointsContainer = document.getElementById('pointsContainer');

        // Responsive values based on screen size - Updated with 15% larger circles
        function getResponsiveValues() {
            const width = window.innerWidth;

            if (width <= 480) {
                return {
                    outerRadius: 287.5, // 250 * 1.15
                    innerRadius: 276,   // 240 * 1.15
                    pointActiveSize: 5,
                    pointInactiveSize: 3,
                    tickLength: 12,
                    smallTickLength: 6
                };
            } else if (width <= 640) {
                return {
                    outerRadius: 287.5, // 250 * 1.15
                    innerRadius: 276,   // 240 * 1.15
                    pointActiveSize: 6,
                    pointInactiveSize: 4,
                    tickLength: 13,
                    smallTickLength: 7
                };
            } else {
                return {
                    outerRadius: 287.5, // 250 * 1.15
                    innerRadius: 276,   // 240 * 1.15
                    pointActiveSize: 7,
                    pointInactiveSize: 5,
                    tickLength: 15,
                    smallTickLength: 8
                };
            }
        }

        // Calculate point positions - Updated for 16 interactive points with 15% larger circles
        function getPointPosition(index, radius = 287.5) { // Updated default radius
            const angle = (index * 360) / 16 - 90; // 16 points around the circle
            const x = Math.cos((angle * Math.PI) / 180) * radius;
            const y = Math.sin((angle * Math.PI) / 180) * radius;
            return { x: 345 + x, y: 345 + y }; // Updated center position for larger viewBox
        }

        function getInnerPointPosition(index, radius = 276) { // Updated default radius
            const angle = (index * 360) / 16 - 90; // 16 points around the circle
            const x = Math.cos((angle * Math.PI) / 180) * radius;
            const y = Math.sin((angle * Math.PI) / 180) * radius;
            return { x: 345 + x, y: 345 + y }; // Updated center position for larger viewBox
        }

        // Get interactive point position - 16 equally spaced points
        function getInteractivePointPosition(index, radius = 276) { // Updated default radius
            // 16 equally spaced points: 144/16 = 9 positions apart
            // Positions: 0, 9, 18, 27, 36, 45, 54, 63, 72, 81, 90, 99, 108, 117, 126, 135
            const position = index * 9; // Each point is 9 positions apart (22.5 degrees)
            const angle = (position * 360) / 144 - 90;
            const x = Math.cos((angle * Math.PI) / 180) * radius;
            const y = Math.sin((angle * Math.PI) / 180) * radius;
            return { x: 345 + x, y: 345 + y }; // Updated center position for larger viewBox
        }

        // Create ripple effect at the arrival point
        function createArrivalRipple(pointIndex) {
            const responsive = getResponsiveValues();
            const pos = getInteractivePointPosition(pointIndex, responsive.innerRadius);

            // Create 3 ripple rings with staggered timing
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    const ripple = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    ripple.setAttribute('cx', pos.x);
                    ripple.setAttribute('cy', pos.y);
                    ripple.setAttribute('r', '3');
                    ripple.setAttribute('fill', 'none');
                    ripple.setAttribute('stroke', '#b9d989');
                    ripple.setAttribute('stroke-width', '2');
                    ripple.setAttribute('opacity', '0.8');
                    ripple.style.filter = 'drop-shadow(0 0 4px #b9d989)';

                    pointsContainer.appendChild(ripple);

                    // Animate ripple expansion
                    let startTime = performance.now();
                    function animateRipple(currentTime) {
                        const elapsed = currentTime - startTime;
                        const progress = Math.min(elapsed / 600, 1);

                        const currentRadius = 3 + (progress * 15);
                        const currentOpacity = 0.8 * (1 - progress);

                        ripple.setAttribute('r', currentRadius);
                        ripple.setAttribute('opacity', currentOpacity);

                        if (progress < 1) {
                            requestAnimationFrame(animateRipple);
                        } else {
                            if (ripple.parentNode) ripple.parentNode.removeChild(ripple);
                        }
                    }

                    requestAnimationFrame(animateRipple);
                }, i * 150);
            }
        }







        // Update content with smooth transition
        function updateContent(newIndex) {
            if (isTransitioning || newIndex === activePoint) return;

            const previousIndex = activePoint;
            isTransitioning = true;

            contentInner.classList.add('transitioning');

            setTimeout(() => {
                const content = contentData[newIndex];
                mainTitle.textContent = content.title;
                subtitle1.textContent = content.subtitle1;
                subtitle2.textContent = content.subtitle2;
                buttonText.textContent = content.buttonText;
                ctaButton.setAttribute('data-link', content.link);

                // Update state tracking
                previousPoint = activePoint; // Mark the current point as previous
                activePoint = newIndex;

                updateProgressRing();
                updatePoints();

                contentInner.classList.remove('transitioning');
                isTransitioning = false;

                // Trigger arrival ripple effect on the CURRENT active point
                setTimeout(() => {
                    createArrivalRipple(activePoint);
                }, 100);
            }, 200);
        }

        // Update progress ring - Updated for 16 points with smooth animation
        function updateProgressRing() {
            const responsive = getResponsiveValues();
            const circumference = 2 * Math.PI * responsive.innerRadius;
            const progress = (activePoint / 16) * circumference; // Fill exactly to the active point
            progressRing.style.strokeDasharray = `${progress} ${circumference}`;
            progressRing.style.transition = 'stroke-dasharray 1.2s ease-in-out';
        }

        // Create progressive fill effect when clicking on a point
        function createProgressiveFill(targetIndex) {
            const responsive = getResponsiveValues();
            const circumference = 2 * Math.PI * responsive.innerRadius;
            const targetProgress = (targetIndex / 16) * circumference;

            // Create temporary fill ring for animation
            const tempRing = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            tempRing.setAttribute('cx', '345');
            tempRing.setAttribute('cy', '345');
            tempRing.setAttribute('r', responsive.innerRadius);
            tempRing.setAttribute('fill', 'none');
            tempRing.setAttribute('stroke', '#b9d989'); // New green color
            tempRing.setAttribute('stroke-width', '4');
            tempRing.setAttribute('stroke-linecap', 'round');
            tempRing.setAttribute('opacity', '0.7');
            tempRing.style.transform = 'rotate(-90deg)';
            tempRing.style.transformOrigin = '345px 345px';

            // Start from 0 (beginning of circle)
            tempRing.style.strokeDasharray = `0 ${circumference}`;
            tempRing.style.strokeDashoffset = '0';

            pointsContainer.appendChild(tempRing);

            // Animate from 0 to target progress (slower animation)
            setTimeout(() => {
                tempRing.style.transition = 'stroke-dasharray 1.5s ease-out';
                tempRing.style.strokeDasharray = `${targetProgress} ${circumference}`;
            }, 50);

            // Remove temp ring after animation (longer delay for slower animation)
            setTimeout(() => {
                if (tempRing.parentNode) {
                    tempRing.parentNode.removeChild(tempRing);
                }
            }, 1700);
        }

        // Update all visual elements - Always recreate to ensure clickable areas work
        function updatePoints() {
            // Always recreate all elements to ensure clickable areas are properly maintained
            createAllElements();
        }

        // Create all elements for the first time
        function createAllElements() {
            pointsContainer.innerHTML = '';
            const responsive = getResponsiveValues();

            // OUTER BORDER - Dense vertical dashes forming the circle outline (shorter height)
            const totalDashes = 144; // Keep original to maintain alignment with large dots (2.5° spacing)
            for (let i = 0; i < totalDashes; i++) {
                const angle = (i * 360) / totalDashes - 90;
                const isMainDash = i % 12 === 0; // Every 12th dash is larger/bolder (12 major dashes total)

                // Check if this dash is near a cardinal position (large dots at positions 0, 36, 72, 108)
                const isNearCardinal = (i % 36 === 0) || (i % 36 === 1) || (i % 36 === 35);

                // Check if this dash is between main dashes (positions 6 and 18 relative to each main dash)
                const isBetweenMain = (i % 12 === 6);

                let dashLength, strokeWidth;
                if (isNearCardinal) {
                    // Dashes near large dots - more prominent (larger and much thicker)
                    dashLength = isMainDash ? 8 : 5.5;
                    strokeWidth = isMainDash ? '3.5' : '2.5'; // Increased width significantly
                } else if (isBetweenMain) {
                    // Dashes between main dashes - increased size, thickness and height
                    dashLength = 8; // Increased height from 6 to 8
                    strokeWidth = '2.5'; // Keep thickness at 2.5
                } else {
                    // Regular dashes
                    dashLength = isMainDash ? 5 : 3;
                    strokeWidth = isMainDash ? '1.5' : '0.8';
                }

                const dashStartRadius = responsive.outerRadius - 1; // Start very close to outer radius
                const dashEndRadius = responsive.outerRadius + dashLength; // Extend outward only

                const radians = (angle * Math.PI) / 180;
                const startX = 345 + Math.cos(radians) * dashStartRadius; // Updated center position
                const startY = 345 + Math.sin(radians) * dashStartRadius; // Updated center position
                const endX = 345 + Math.cos(radians) * dashEndRadius;     // Updated center position
                const endY = 345 + Math.sin(radians) * dashEndRadius;     // Updated center position

                const dash = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                dash.setAttribute('x1', startX);
                dash.setAttribute('y1', startY);
                dash.setAttribute('x2', endX);
                dash.setAttribute('y2', endY);
                dash.setAttribute('stroke', '#fefefe');
                dash.setAttribute('stroke-width', strokeWidth);
                dash.setAttribute('stroke-linecap', 'round');
                pointsContainer.appendChild(dash);
            }

            // INNER CIRCLE - Dense dot pattern with 4 large dots at cardinal positions
            const totalPositions = 216; // Increased for more small dots (1.67° spacing)

            for (let i = 0; i < totalPositions; i++) {
                const angle = (i * 360) / totalPositions - 90;
                const dotRadius = responsive.innerRadius;

                const dotX = 345 + Math.cos((angle * Math.PI) / 180) * dotRadius; // Updated center position
                const dotY = 345 + Math.sin((angle * Math.PI) / 180) * dotRadius; // Updated center position

                const dot = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                dot.setAttribute('cx', dotX);
                dot.setAttribute('cy', dotY);
                dot.setAttribute('data-position', i); // Add position identifier

                // Determine dot size and style based on position
                // 16 equally spaced interactive positions: scaled for 216 total positions
                const interactivePositions = [0, 13, 27, 40, 54, 67, 81, 94, 108, 121, 135, 148, 162, 175, 189, 202];
                const isInteractivePosition = interactivePositions.includes(i);
                const isCardinalPosition = i === 0 || i === 54 || i === 108 || i === 162; // Main cardinal directions (scaled for 216)

                if (isCardinalPosition) {
                    // 4 large dots at cardinal directions (clickable)
                    const interactiveIndex = interactivePositions.indexOf(i);
                    const isActive = interactiveIndex === activePoint;
                    const isPrevious = interactiveIndex === (activePoint - 1 + 16) % 16;
                    const isNext = interactiveIndex === (activePoint + 1) % 16;

                    // Create invisible larger clickable area first
                    const clickArea = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    clickArea.setAttribute('cx', dotX);
                    clickArea.setAttribute('cy', dotY);
                    clickArea.setAttribute('r', '15'); // Much larger clickable area
                    clickArea.setAttribute('fill', 'transparent');
                    clickArea.setAttribute('stroke', 'none');
                    clickArea.style.cursor = 'pointer';
                    clickArea.style.pointerEvents = 'all'; // Ensure it captures clicks
                    clickArea.setAttribute('data-interactive-index', interactiveIndex);
                    clickArea.setAttribute('data-click-type', 'large');

                    // Add click handler to the invisible area
                    clickArea.addEventListener('click', function(e) {
                        e.stopPropagation();
                        handlePointClick(interactiveIndex);
                    });

                    pointsContainer.appendChild(clickArea);

                    dot.setAttribute('r', '7.5');
                    // Set color based on state
                    if (isActive) {
                        dot.setAttribute('fill', '#b9d989'); // New green for active
                    } else if (isNext || isPrevious) {
                        dot.setAttribute('fill', '#b9d989'); // New green for next/previous
                    } else {
                        dot.setAttribute('fill', '#fefefe'); // New white for others
                    }
                    dot.setAttribute('opacity', '1');
                    dot.style.cursor = 'pointer';
                    dot.style.transition = 'all 0.3s ease';
                    dot.style.zIndex = '1000'; // Bring to front

                    // Add fallback click handler to the visible dot as well
                    dot.addEventListener('click', function(e) {
                        e.stopPropagation();
                        handlePointClick(interactiveIndex);
                    });

                    // Add hover effects to the invisible clickable area (will affect the visible dot)
                    clickArea.addEventListener('mouseenter', function() {
                        if (!isActive && !isNext && !isPrevious) {
                            dot.setAttribute('fill', '#b9d989');
                            dot.setAttribute('r', '8.5');
                            dot.style.filter = 'drop-shadow(0 0 8px #b9d989)';
                        }
                    });

                    clickArea.addEventListener('mouseleave', function() {
                        if (!isActive && !isNext && !isPrevious) {
                            dot.setAttribute('fill', '#fefefe');
                            dot.setAttribute('r', '7.5');
                            dot.style.filter = 'none';
                        }
                    });

                } else if (isInteractivePosition) {
                    // 12 medium dots at equally spaced positions (clickable)
                    const interactiveIndex = interactivePositions.indexOf(i);
                    const isActive = interactiveIndex === activePoint;
                    const isPrevious = interactiveIndex === (activePoint - 1 + 16) % 16;
                    const isNext = interactiveIndex === (activePoint + 1) % 16;

                    // Create invisible larger clickable area first
                    const clickArea = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    clickArea.setAttribute('cx', dotX);
                    clickArea.setAttribute('cy', dotY);
                    clickArea.setAttribute('r', '12'); // Larger clickable area for medium dots
                    clickArea.setAttribute('fill', 'transparent');
                    clickArea.setAttribute('stroke', 'none');
                    clickArea.style.cursor = 'pointer';
                    clickArea.style.pointerEvents = 'all'; // Ensure it captures clicks
                    clickArea.setAttribute('data-interactive-index', interactiveIndex);
                    clickArea.setAttribute('data-click-type', 'medium');

                    // Add click handler to the invisible area
                    clickArea.addEventListener('click', function(e) {
                        e.stopPropagation();
                        handlePointClick(interactiveIndex);
                    });

                    pointsContainer.appendChild(clickArea);

                    dot.setAttribute('r', '3.5');
                    // Set color based on state
                    if (isActive) {
                        dot.setAttribute('fill', '#b9d989'); // New green for active
                    } else if (isNext || isPrevious) {
                        dot.setAttribute('fill', '#b9d989'); // New green for next/previous
                    } else {
                        dot.setAttribute('fill', '#fefefe'); // New white for others
                    }
                    dot.setAttribute('opacity', '0.8');
                    dot.style.cursor = 'pointer';
                    dot.style.transition = 'all 0.3s ease';
                    dot.style.zIndex = '1000'; // Bring to front

                    // Add fallback click handler to the visible dot as well
                    dot.addEventListener('click', function(e) {
                        e.stopPropagation();
                        handlePointClick(interactiveIndex);
                    });

                    // Add hover effects to the invisible clickable area (will affect the visible dot)
                    clickArea.addEventListener('mouseenter', function() {
                        if (!isActive && !isNext && !isPrevious) {
                            dot.setAttribute('fill', '#b9d989');
                            dot.setAttribute('r', '4.5');
                            dot.style.filter = 'drop-shadow(0 0 6px #b9d989)';
                        }
                    });

                    clickArea.addEventListener('mouseleave', function() {
                        if (!isActive && !isNext && !isPrevious) {
                            dot.setAttribute('fill', '#fefefe');
                            dot.setAttribute('r', '3.5');
                            dot.style.filter = 'none';
                        }
                    });

                } else {
                    // Very small dots filling the rest (lighter grey color, non-interactive)
                    dot.setAttribute('r', '0.8'); // Reduced from 1.2 to 0.8
                    dot.setAttribute('fill', '#fefefe'); // New white for small dots
                    dot.setAttribute('opacity', '0.5'); // Reduced opacity
                }

                pointsContainer.appendChild(dot);
            }

            // INNER BORDER - Flow indicators (REMOVED)
            // The straight lines and traveling effects have been removed
            // Only the interactive points remain

            // INNER BORDER - Interactive points (16 total: 4 large + 12 medium)
            for (let i = 0; i < 16; i++) {
                const isActive = i === activePoint;
                const isPrevious = i === (activePoint - 1 + 16) % 16;
                const isNext = i === (activePoint + 1) % 16; // Next point (one forward)

                // Determine if this is a large dot (cardinal) or medium dot
                const isLargeDot = i % 4 === 0; // Indices 0, 4, 8, 12 are large dots

                // Glow effects - show on active point (current point) - with pointer-events: none
                if (isActive) {
                    const glow = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    const glowPos = getInteractivePointPosition(i, responsive.innerRadius);
                    glow.setAttribute('cx', glowPos.x);
                    glow.setAttribute('cy', glowPos.y);
                    glow.setAttribute('r', (isLargeDot ? responsive.pointActiveSize * 2.5 : responsive.pointActiveSize * 1.8));
                    glow.setAttribute('fill', 'rgba(185, 217, 137, 0.3)'); // #b9d989 with opacity
                    glow.style.pointerEvents = 'none'; // Allow clicks to pass through
                    glow.classList.add('ping');
                    pointsContainer.appendChild(glow);
                }

                if (isPrevious) {
                    const prevGlow = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    const prevGlowPos = getInteractivePointPosition(i, responsive.innerRadius);
                    prevGlow.setAttribute('cx', prevGlowPos.x);
                    prevGlow.setAttribute('cy', prevGlowPos.y);
                    prevGlow.setAttribute('r', (isLargeDot ? responsive.pointActiveSize * 2 : responsive.pointActiveSize * 1.3));
                    prevGlow.setAttribute('fill', 'rgba(134, 239, 172, 0.2)');
                    prevGlow.style.pointerEvents = 'none'; // Allow clicks to pass through
                    prevGlow.classList.add('pulse-slow');
                    pointsContainer.appendChild(prevGlow);
                }


            }
        }

        // Update existing elements without recreating them
        function updateExistingElements() {
            const responsive = getResponsiveValues();
            const interactivePositions = [0, 13, 27, 40, 54, 67, 81, 94, 108, 121, 135, 148, 162, 175, 189, 202];

            // Clear only glow effects (they need to be recreated for positioning)
            const glowElements = pointsContainer.querySelectorAll('.ping, .pulse-slow');
            glowElements.forEach(el => el.remove());

            // Update colors of existing interactive dots
            const allDots = pointsContainer.querySelectorAll('circle[data-position]');
            allDots.forEach((dot) => {
                const position = parseInt(dot.getAttribute('data-position'));
                if (interactivePositions.includes(position)) {
                    const interactiveIndex = interactivePositions.indexOf(position);
                    const isActive = interactiveIndex === activePoint;
                    const isPrevious = interactiveIndex === (activePoint - 1 + 16) % 16;
                    const isNext = interactiveIndex === (activePoint + 1) % 16;

                    // Update colors based on state
                    if (isActive) {
                        dot.setAttribute('fill', '#b9d989');
                    } else if (isNext || isPrevious) {
                        dot.setAttribute('fill', '#b9d989');
                    } else {
                        const isCardinal = position === 0 || position === 54 || position === 108 || position === 162;
                        dot.setAttribute('fill', '#fefefe');
                    }
                }
            });

            // Recreate glow effects
            for (let i = 0; i < 16; i++) {
                const isActive = i === activePoint;
                const isPrevious = i === (activePoint - 1 + 16) % 16;
                const isLargeDot = i % 4 === 0;

                if (isActive) {
                    const glow = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    const glowPos = getInteractivePointPosition(i, responsive.innerRadius);
                    glow.setAttribute('cx', glowPos.x);
                    glow.setAttribute('cy', glowPos.y);
                    glow.setAttribute('r', (isLargeDot ? responsive.pointActiveSize * 2.5 : responsive.pointActiveSize * 1.8));
                    glow.setAttribute('fill', 'rgba(185, 217, 137, 0.3)'); // #b9d989 with opacity
                    glow.style.pointerEvents = 'none';
                    glow.classList.add('ping');
                    pointsContainer.appendChild(glow);
                }

                if (isPrevious) {
                    const prevGlow = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    const prevGlowPos = getInteractivePointPosition(i, responsive.innerRadius);
                    prevGlow.setAttribute('cx', prevGlowPos.x);
                    prevGlow.setAttribute('cy', prevGlowPos.y);
                    prevGlow.setAttribute('r', (isLargeDot ? responsive.pointActiveSize * 2 : responsive.pointActiveSize * 1.3));
                    prevGlow.setAttribute('fill', 'rgba(134, 239, 172, 0.2)');
                    prevGlow.style.pointerEvents = 'none';
                    prevGlow.classList.add('pulse-slow');
                    pointsContainer.appendChild(prevGlow);
                }
            }
        }

        // Handle point clicks with progressive fill effect
        function handlePointClick(index) {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
            }
            isAutoPlaying = false;

            // Create progressive fill effect to the clicked point
            createProgressiveFill(index);

            // Update content after a short delay to allow fill animation to start
            setTimeout(() => {
                updateContent(index);
            }, 100);

            // Resume auto-play after 8 seconds
            setTimeout(() => {
                isAutoPlaying = true;
                startAutoPlay();
            }, 8000);
        }

        // Auto-play functionality - EXACT COPY FROM ORIGINAL
        function startAutoPlay() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
            }

            if (!isAutoPlaying) return;

            autoPlayInterval = setInterval(() => {
                if (!isAutoPlaying) return;
                const nextIndex = (activePoint + 1) % 16; // Updated for 16 interactive points
                updateContent(nextIndex);
            }, 4000);
        }

        // Button click handler - EXACT COPY FROM ORIGINAL
        ctaButton.addEventListener('click', function() {
            const link = this.getAttribute('data-link');
            const currentContent = contentData[activePoint];

            if (link && link.startsWith('#')) {
                const targetElement = document.querySelector(link);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                } else {
                    alert(`${currentContent.buttonText} - ${currentContent.title}\nSection: ${link}`);
                }
            } else if (link) {
                window.open(link, '_blank');
            } else {
                alert(`${currentContent.buttonText} - ${currentContent.title}`);
            }
        });

        // Update SVG dimensions for responsiveness
        function updateSVGDimensions() {
            const responsive = getResponsiveValues();

            const outerCircle = document.querySelector('circle[r="287.5"]'); // Updated to match new radius
            const innerCircle = document.querySelector('circle[r="276"]');   // Updated to match new radius
            const progressRing = document.getElementById('progressRing');

            if (outerCircle) outerCircle.setAttribute('r', responsive.outerRadius);
            if (innerCircle) innerCircle.setAttribute('r', responsive.innerRadius);
            if (progressRing) progressRing.setAttribute('r', responsive.innerRadius);
        }

        // Initialize everything - EXACT COPY FROM ORIGINAL
        function init() {
            updateSVGDimensions();
            updateProgressRing();
            updatePoints();
            startAutoPlay();

            // Responsive handlers
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    updateSVGDimensions();
                    updateProgressRing();
                    updatePoints();
                }, 250);
            });

            window.addEventListener('orientationchange', () => {
                setTimeout(() => {
                    updateSVGDimensions();
                    updateProgressRing();
                    updatePoints();
                }, 300);
            });
        }

        // Start the application
        init();
    </script>
</body>
</html>

