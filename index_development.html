
<!DOCTYPE html>
<html lang="en">

<head>
 
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Home || Cyber Shelter</title>
    <link rel="icon" type="image/png" href="images-2/new/favicon.png">

    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,400;9..40,500;9..40,600;9..40,700;9..40,800&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@500;600;700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Castoro&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Schoolbell&amp;display=swap" rel="stylesheet">


    <link rel="stylesheet" href="assets/vendors/bootstrap/css/bootstrap.min.css" />
    <link rel="stylesheet" href="assets/vendors/bootstrap-select/bootstrap-select.min.css" />
    <link rel="stylesheet" href="assets/vendors/animate/animate.min.css" />
    <link rel="stylesheet" href="assets/vendors/fontawesome/css/all.min.css" />
    <link rel="stylesheet" href="assets/vendors/jquery-ui/jquery-ui.css" />
    <link rel="stylesheet" href="assets/vendors/jarallax/jarallax.css" />
    <link rel="stylesheet" href="assets/vendors/jquery-magnific-popup/jquery.magnific-popup.css" />
    <link rel="stylesheet" href="assets/vendors/nouislider/nouislider.min.css" />
    <link rel="stylesheet" href="assets/vendors/nouislider/nouislider.pips.css" />
    <link rel="stylesheet" href="assets/vendors/tiny-slider/tiny-slider.css" />
    <link rel="stylesheet" href="assets/vendors/cleenhearts-icons/style.css" />
    <link rel="stylesheet" href="assets/vendors/swiper/css/swiper-bundle.min.css" />
    <link rel="stylesheet" href="assets/vendors/owl-carousel/css/owl.carousel.min.css" />
    <link rel="stylesheet" href="assets/vendors/owl-carousel/css/owl.theme.default.min.css" />
    <link rel="stylesheet" href="assets/css/style.css" />
    <link href="assets-2/css/main.css" rel="stylesheet" type="text/css">
    <script src="ScriptResource_d%3DokuX3IVIBwfJlfEQK32K3hLp5JzXdmgd16aPokzkQNuBO0MGfIs2avfEZRz4gmZKgh6KmzPc4EYDfdA8ogEs1JJwG0eSNj_UZkni9ZCTmD68hH_okEI5LcMO6sGh_Xsn8fLDIylyAq5iNhg3XKJ4e_uUsewq1B5uInNFhQrSfC81FXD8QbtUF6QnT9mbJk2X0%26t%3D6262bd8b.axd"
        type="text/javascript"></script>
    
    
    <style>
        .banner-container {
            position: relative;
            width: 100%;
            height: 100vh;
            background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.7)), 
                              url('images-2/new/agile.mp4');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        /* Business background effect */
        .banner-container::before {
            content: '';
            position: absolute;
            inset: 0;
            background: radial-gradient(circle at 30% 40%, rgba(100, 116, 139, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 70% 60%, rgba(71, 85, 105, 0.2) 0%, transparent 50%);
            z-index: 1;
        }

        .main-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .circular-component {
            position: relative;
            width: 100%;
            max-width: 700px;
            margin: 0 auto;
            aspect-ratio: 1/1;
        }

        .central-content {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 5;
        }

        .content-inner {
            text-align: center;
            transition: opacity 0.4s ease-in-out;
        }

        .content-inner.transitioning {
            opacity: 0;
        }

        .main-title {
            font-size: 4rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
            line-height: 1;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .subtitle-line {
            font-size: 4rem;
            font-weight: 700;
            color: white;
            line-height: 1;
            margin-bottom: 0.25rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .subtitle-line:last-of-type {
            margin-bottom: 3rem;
        }

        .cta-button {
            background: rgba(31, 41, 55, 0.9);
            backdrop-filter: blur(10px);
            color: white;
            padding: 1rem 2.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.125rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .cta-button:hover {
            background: rgba(55, 65, 81, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        .svg-container {
            position: absolute;
            inset: 0;
            z-index: 1;
        }

        .circular-svg {
            width: 100%;
            height: 100%;
        }

        /* Circle styling */
        .circle-border {
            fill: none;
            stroke: rgba(255, 255, 255, 0.4);
            stroke-width: 2;
            stroke-dasharray: 8 8;
        }

        .progress-ring {
            fill: none;
            stroke: #86efac;
            stroke-width: 3;
            stroke-linecap: round;
            transition: stroke-dasharray 1s ease-out;
        }

        .tick-mark {
            stroke: rgba(255, 255, 255, 0.8);
            stroke-width: 1;
            stroke-linecap: round;
        }

        .tick-mark-bold {
            stroke: rgba(255, 255, 255, 1);
            stroke-width: 2;
            stroke-linecap: round;
        }

        .tick-mark-small {
            stroke: rgba(255, 255, 255, 0.4);
            stroke-width: 1;
            stroke-linecap: round;
        }

        /* Animations */
        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.5; }
        }

        @keyframes ping {
            0% { opacity: 0.3; transform: scale(1); }
            75% { opacity: 0; transform: scale(1.5); }
            100% { opacity: 0; transform: scale(1.5); }
        }

        .pulse-slow {
            animation: pulse 3s ease-in-out infinite;
        }

        .ping {
            animation: ping 2s ease-in-out infinite;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 480px) {
            .main-title, .subtitle-line {
                font-size: 1.5rem;
            }
            .circular-component {
                max-width: 320px;
            }
            .cta-button {
                padding: 0.5rem 1.25rem;
                font-size: 0.8rem;
                gap: 0.5rem;
            }
            .main-container {
                padding: 1rem;
            }
        }

        @media (min-width: 481px) and (max-width: 640px) {
            .main-title, .subtitle-line {
                font-size: 1.875rem;
            }
            .circular-component {
                max-width: 400px;
            }
            .cta-button {
                padding: 0.625rem 1.5rem;
                font-size: 0.875rem;
            }
        }

        @media (min-width: 641px) and (max-width: 768px) {
            .main-title, .subtitle-line {
                font-size: 2.5rem;
            }
            .circular-component {
                max-width: 500px;
            }
            .cta-button {
                padding: 0.75rem 1.75rem;
                font-size: 0.95rem;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .main-title, .subtitle-line {
                font-size: 3.5rem;
            }
            .circular-component {
                max-width: 600px;
            }
            .cta-button {
                padding: 0.875rem 2rem;
                font-size: 1rem;
            }
        }

        @media (min-width: 1025px) and (max-width: 1280px) {
            .main-title, .subtitle-line {
                font-size: 4rem;
            }
            .circular-component {
                max-width: 650px;
            }
            .cta-button {
                padding: 1rem 2.25rem;
                font-size: 1.075rem;
            }
        }

        @media (min-width: 1281px) and (max-width: 1536px) {
            .main-title, .subtitle-line {
                font-size: 4.5rem;
            }
            .circular-component {
                max-width: 700px;
            }
            .cta-button {
                padding: 1rem 2.5rem;
                font-size: 1.125rem;
            }
        }

        @media (min-width: 1537px) {
            .main-title, .subtitle-line {
                font-size: 5rem;
            }
            .circular-component {
                max-width: 800px;
            }
            .cta-button {
                padding: 1.125rem 2.75rem;
                font-size: 1.25rem;
            }
        }

        /* Landscape and touch optimizations */
        @media (max-height: 600px) and (orientation: landscape) {
            .banner-container {
                min-height: 100vh;
                padding: 1rem 0;
            }
            .main-title, .subtitle-line {
                font-size: 1.5rem !important;
            }
            .circular-component {
                max-width: 400px !important;
            }
            .subtitle-line:last-of-type {
                margin-bottom: 1.5rem;
            }
        }

        @media (hover: none) and (pointer: coarse) {
            .cta-button:hover {
                transform: none;
            }
        }
    </style>
</head>  











<body class="section-scroll-enable
      
       normal-mode corporate-site page_a53bb6ab72274169ac31cc6769e98074" style=""> 

 



       
  <div class="page-wrapper">
<header class="site-header mo-left header" style="height: 100px; background: rgba(200, 211, 213, 0.03); box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1); backdrop-filter: blur(11.6px); -webkit-backdrop-filter: blur(11.6px); width: 100%;">
  <div class="sticky-header main-bar-wraper navbar-expand-lg" style="height: 79px; background: rgba(200, 211, 213, 0.03); box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1); backdrop-filter: blur(11.6px); -webkit-backdrop-filter: blur(11.6px); height: 100px;">
    <div class="main-bar clearfix">
      <div class="container clearfix">
        <div class="d-flex" style="gap:150px;">
          <div class="logo-header" style="display: block; display: none;">
            <a href="index.html">
              <img src="images-2/new/CyberShelter.png" alt="" style="width: 200px;" class="mt-2 mb-2">
            </a>
          </div>
          <button class="navbar-toggler navicon mt-5" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>

        <div class="header-nav navbar-collapse collapse" id="navbarNavDropdown" style="background-color: transparent; justify-content: space-between;">
          <div class="logo-header d-md-block">
            <a href="index.html"><img src="images-2/new/CyberShelter.png" alt="" style="width: 250px;" class="mt-2 mb-2"></a>
          </div>
          <div style="display: flex; flex-direction: column;">
            <ul class="nav navbar-nav">
              <li><a href="index.html" style="color: white; text-decoration: none;" onmouseover="this.style.color='white';" onmouseout="this.style.color='white';" onfocus="this.style.color='white';" onblur="this.style.color='white';">Home</a></li>
              <li class="has-mega-menu">
                <a href="#" style="color: white; text-decoration: none;" onmouseover="this.style.color='white';" onmouseout="this.style.color='white';" onfocus="this.style.color='white';" onblur="this.style.color='white';">Who We Are <i class="fa fa-chevron-down"></i></a>
                <ul class="mega-menu">
                  <li>
                    <a href="#"></a>
                    <ul>
                      <li><a href="founded.html">Founded by Industry Stalwarts</a></li>
                      <li><a href="industry.html">Industry Experience</a></li>
                      <li><a href="fundamentals.html">Based on Fundamentals</a></li>
                      <li><a href="business-understanding.html">Business Understanding</a></li>
                      <li><a href="flexible.html">Flexible</a></li>
                      <li><a href="vision-mission.html">Vision and Mission</a></li>
                      <li><a href="holistic.html">Holistic</a></li>
                      <li><a href="agile.html">Agile</a></li>
                      <li><a href="practical.html">Practical</a></li>
                    </ul>
                  </li>
                  <li>
                    <ul>
                      <li style="font-size: 28px; padding: 10px 25px; border-bottom: none; color: #fff;" class="nav_title">Comprehensive Cybersecurity Solutions</li>
                      <li>
                        <img src="images-2/240_F_480884814_busW8r7P4VroKSGjCupiyBekzGT61kVF.jpg" alt="Cybersecurity Services" style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">We provide a full range of cybersecurity services, from threat detection to advanced incident response, ensuring your digital environment is always protected.</p>
                      </li>
                    </ul>
                  </li>
                  <li>
                    <ul>
                      <li style="font-size: 28px; padding: 10px 25px; border-bottom: none; color: #fff;" class="nav_title">Managed Security Services</li>
                      <li>
                        <img src="images-2/business-cyber.jpg" alt="Managed Security" style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">Our managed security services provide round-the-clock monitoring, proactive threat hunting, and quick incident response to keep your systems secure at all times.</p>
                      </li>
                    </ul>
                  </li>
                </ul>
              </li>
              <li class="has-mega-menu">
                <a href="#" style="color: white; text-decoration: none;" onmouseover="this.style.color='white';" onmouseout="this.style.color='white';" onfocus="this.style.color='white';" onblur="this.style.color='white';">What We Do<i class="fa fa-chevron-down"></i></a>
                <ul class="mega-menu">
                  <li>
                    <a href="#"></a>
                    <ul>
                      <li><a href="professional-services.html">Professional Services</a></li>
                      <li><a href="managed-services.html">Managed Services</a></li>
                      <li><a href="solutions.html">Solutions</a></li>
                    </ul>
                  </li>
                  <li>
                    <ul>
                      <li style="font-size: 28px; padding: 10px 25px; border-bottom: none; color: #fff;" class="nav_title">Penetration Testing</li>
                      <li>
                        <img src="images-2/21839.jpg" alt="Penetration Testing" style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">We conduct thorough penetration testing to identify vulnerabilities in your systems before attackers can exploit them, ensuring your defenses are robust.</p>
                      </li>
                    </ul>
                  </li>
                  <li>
                    <ul>
                      <li style="font-size: 28px; padding: 10px 25px; border-bottom: none; color: #fff;" class="nav_title">Compliance & Risk Management</li>
                      <li>
                        <img src="images-2/news-1.webp" alt="Compliance and Risk" style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">We help your organization meet industry standards and regulatory requirements, providing expert guidance in risk management and compliance to protect your reputation.</p>
                      </li>
                    </ul>
                  </li>
                </ul>
              </li>
              <li class="has-mega-menu">
                <a href="#" style="color: white; text-decoration: none;" onmouseover="this.style.color='white';" onmouseout="this.style.color='white';" onfocus="this.style.color='white';" onblur="this.style.color='white';">Buzz/What’s hot<i class="fa fa-chevron-down"></i></a>
                <ul class="mega-menu">
                  <li>
                    <a href="#"></a>
                    <ul>
                      <li><a href="ai-security.html">AI Security</a></li>
                      <li><a href="api-security.html">API Security</a></li>
                      <li><a href="trpm.html">TPRM</a></li>
                    </ul>
                  </li>
                  <li>
                    <ul>
                      <li style="font-size: 28px; padding: 10px 25px; border-bottom: none; color: #fff;" class="nav_title">Latest Cybersecurity Threats</li>
                      <li>
                        <img src="images-2/view-people-addicted-their-smartphone-looking-scrolling-through-screens.jpg" alt="Cybersecurity Threats" style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">Stay informed about the latest cybersecurity threats and attacks targeting organizations worldwide. We provide timely updates on emerging threats to help you stay ahead of potential risks.</p>
                      </li>
                    </ul>
                  </li>
                  <li>
                    <ul>
                      <li style="font-size: 28px; padding: 10px 25px; border-bottom: none; color: #fff;" class="nav_title">Cybersecurity Industry Insights</li>
                      <li>
                        <img src="images-2/medium-shot-woman-working-computer_23-2150287666.webp" alt="Cybersecurity Insights" style="width: 100%; max-width: 400px;">
                        <p style="color: #fff;">Get the latest industry insights, trends, and best practices in the world of cybersecurity. Our news section covers key developments to help you understand the evolving landscape of digital security.</p>
                      </li>
                    </ul>
                  </li>
                </ul>
              </li>
              <li><a href="careers.html" style="color: white; text-decoration: none;" onmouseover="this.style.color='white';" onmouseout="this.style.color='white';" onfocus="this.style.color='white';" onblur="this.style.color='white';">Careers</a></li>
              <li><a href="contact.html" style="color: white; text-decoration: none;" onmouseover="this.style.color='white';" onmouseout="this.style.color='white';" onfocus="this.style.color='white';" onblur="this.style.color='white';">Contact</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
	 <div>

            <div class="hero-banner" style="background:transparent!important;">
                <div id="main" class="home-main">
                    <div id="MainTop_T03A6BEBD001_Col00" class="sub-main-banner sf_colsIn" data-sf-element="Content"
                        data-placeholder-label="Content">
                        
                        <div class="shadow">
                            <div class="mobile-gradient"></div>
                        </div>
             

<div>


                        <div class="section-to-scroll" data-color="dark" id="Home" data-section-name="Home" style="background: url('images-2/new/banner-main.gif') no-repeat center center;
!important;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;padding-bottom:50px;">
                            <h1 class="hidden-title">Cyber Shelter</h1>
                            <div id="video-container" class="vid-container">
                                <button id="close" class="close-btn no-display">X</button> 
                                <div id="video-even" class="vid-even video-track">   
                                    <video playsinline="" muted="" id="video-container-even" preload="metadata"
                                        class="bg-video no-display">
                                        Your browser does not support HTML5 video.
                                    </video>
                                </div>
                                <div id="video-odd" class="vid-odd video-track">
                                    <video playsinline="" muted="" id="video-container-odd" preload="metadata"
                                        class="bg-video no-display" src="">
                                        Your browser does not support HTML5 video.
                                    </video>
                                </div>
                                <div class="di-main no-display" id="dial-main"  style="margin-top: 20px;width: 713px;display: block;">
                                    <ul class="di-main-hotspots-svg" id="dial-main-hotspots-svg">
                                        <li class="hotspot main-hotspot ripple
                            
                                            "id="main-hotspot-0" data-hour="0" data-quotes="#"
    
                                            data-cta-url="#"
                                            data-video-src="https://trogon.info/website/cs-new/images-2/new/video-2.mp4"
                                            data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/video-2.mp4" style="position:absolute;z-index:99999!important;">
                                            
                                        </li> 

                                        <li class="hotspot main-hotspot ripple
                            
                            " id="main-hotspot-6" data-hour="6" data-quotes="#"
                                            
                                            data-cta-url="#"
                                            data-video-src="#"
                                            data-video-src-mobile="#">
                                            
                                        </li>


                                        <li class="hotspot main-hotspot ripple
                            
                            " id="main-hotspot-12" data-hour="12" data-quotes="#"
                                            
                                            data-cta-url="#"
                                            data-video-src="#"
                                            data-video-src-mobile="#">
                                            
                                        </li>

                                        <li class="hotspot main-hotspot ripple
                            
                            " id="main-hotspot-18" data-hour="18" data-quotes="#"
                                            
                                            data-cta-url="#"
                                            data-video-src="#"
                                            data-video-src-mobile="#">
                                            
                                        </li>

                                    </ul>
                                    <div class="di-main-wrapper" style="opacity:0.5;" >
                                        <div id="welcome-msg" class="content-dial no-display" >
                                            <h2 id="welcome-msg-text" class="content-dial-txt welcome-msg-txt">Safeguarding Your Digital Realm</h2>
                                        </div>

                                        <svg class="di-main-svg no-display" id="dial-main-svg" viewBox="0 0 100 100"
                                            xmlns="http://www.w3.org/2000/svg" version="1.1"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            style="transform: rotate(0deg) scale(1); display: inline; touch-action: none;width: 600px;">

                                            <path id="dial-progress-value" class="di-progress-value"
                                                fill="rgba(0,0,0,0)" stroke="rgba(175, 214, 137, 1)" stroke-width="0.4"
                                                d="M 94.43258800929715 38.094323925284044 A 46 46 0 0 0 50 4"></path>
                                            <g id="rotate-area"
                                                transform="matrix(-0.79863,0.60181,-0.60181,-0.79863,120.02252665996706,59.84102434476223)">
                                                <circle id="dial-progress-path" class="di-progress-bar" stroke-width="1"
                                                    cx="50" cy="50" r="50" fill="rgba(0,0,0,0)" stroke="rgba(0,0,0,0)">
                                                </circle>
                                            </g>
                                            <circle id="dial-drag-patch" class="drag-patch" stroke-width="1" cx="50"
                                                cy="50" r="45" fill="rgba(0,0,0,0)" stroke="rgba(0,0,0,0)"></circle>
                                            <defs id="SvgjsDefs1006"></defs>
                                            <circle cx="50" cy="4" r="1.3" fill="#ccc" transform="rotate(0,50,50)"
                                                class="inner-point base-fill gr-dot main-dot" id="inner-pt-90"></circle>
                                            <rect x="49.4" y="-1" fill="rgba(175, 214, 137, 1)" transform="rotate(0,50,50)"
                                                class="hotspot m-hp ripple" height="3.300000000000001px"
                                                width="0.8999999999999999px" id="dash-90"></rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(-1,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-90" data-hour="6"
                                                data-quotes="Quick, Efficient, and Adaptive Solutions"
                                                
                                                data-cta-url="holistic.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/video-2.mp4"
                                                data-video-src-mobile="">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)" transform="rotate(1.5,50,50)"
                                                class="inner-point" id="inner-pt-91.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(1.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(3,50,50)"
                                                class="inner-point" id="inner-pt-93"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(3,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(4.5,50,50)"
                                                class="inner-point" id="inner-pt-94.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(4.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(6,50,50)"
                                                class="inner-point" id="inner-pt-96"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(6,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(7.5,50,50)"
                                                class="inner-point" id="inner-pt-97.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(7.5,50,50)" class="hotspot"
                                                height="2.1px" width="0.3px" id="dash-97.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(9,50,50)"
                                                class="inner-point" id="inner-pt-99"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(9,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(10.5,50,50)"
                                                class="inner-point" id="inner-pt-100.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(10.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(12,50,50)"
                                                class="inner-point" id="inner-pt-102"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(12,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(13.5,50,50)"
                                                class="inner-point" id="inner-pt-103.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(13.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(15,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-105"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(15,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-105">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(14,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-105" data-hour="7"
                                                data-quotes="Industry
Experience"
                                                
                                              data-cta-url="industry.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/practical.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/practical.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(16.5,50,50)"
                                                class="inner-point" id="inner-pt-106.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(16.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(18,50,50)"
                                                class="inner-point" id="inner-pt-108"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(18,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(19.5,50,50)"
                                                class="inner-point" id="inner-pt-109.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(19.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(21,50,50)"
                                                class="inner-point" id="inner-pt-111"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(21,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(22.5,50,50)"
                                                class="inner-point" id="inner-pt-112.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(22.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-112.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(24,50,50)"
                                                class="inner-point" id="inner-pt-114"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(24,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(25.5,50,50)"
                                                class="inner-point" id="inner-pt-115.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(25.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(27,50,50)"
                                                class="inner-point" id="inner-pt-117"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(27,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(28.5,50,50)"
                                                class="inner-point" id="inner-pt-118.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(28.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(30,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-120"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(30,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-120">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(29,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-120" data-hour="8"
                                                data-quotes="Strategic Security for Complex Challenges"
                                                
                                                data-cta-url="business.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/flexible.mp4"
                                                data-video-src-mobile="">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(31.5,50,50)"
                                                class="inner-point" id="inner-pt-121.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(31.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(33,50,50)"
                                                class="inner-point" id="inner-pt-123"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(33,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(34.5,50,50)"
                                                class="inner-point" id="inner-pt-124.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(34.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(36,50,50)"
                                                class="inner-point" id="inner-pt-126"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(36,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(37.5,50,50)"
                                                class="inner-point" id="inner-pt-127.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(37.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-127.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(39,50,50)"
                                                class="inner-point" id="inner-pt-129"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(39,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(40.5,50,50)"
                                                class="inner-point" id="inner-pt-130.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(40.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(42,50,50)"
                                                class="inner-point" id="inner-pt-132"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(42,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(43.5,50,50)"
                                                class="inner-point" id="inner-pt-133.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(43.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(45,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-135"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(45,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-135">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(44,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-135" data-hour="9"
                                                data-quotes="Empowering Businesses with Confidence"
                                                
                                                data-cta-url="business-understanding.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/video-1.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/video-1.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(46.5,50,50)"
                                                class="inner-point" id="inner-pt-136.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(46.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(48,50,50)"
                                                class="inner-point" id="inner-pt-138"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(48,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(49.5,50,50)"
                                                class="inner-point" id="inner-pt-139.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(49.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(51,50,50)"
                                                class="inner-point" id="inner-pt-141"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(51,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(52.5,50,50)"
                                                class="inner-point" id="inner-pt-142.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(52.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-142.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(54,50,50)"
                                                class="inner-point" id="inner-pt-144"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(54,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(55.5,50,50)"
                                                class="inner-point" id="inner-pt-145.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(55.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(57,50,50)"
                                                class="inner-point" id="inner-pt-147"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(57,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(58.5,50,50)"
                                                class="inner-point" id="inner-pt-148.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(58.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(60,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-150"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(60,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-150">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(59,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-150" data-hour="10"
                                                data-quotes="Flexible"
                                                
                                                data-cta-url="flexible.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/flexible.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/flexible.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(61.5,50,50)"
                                                class="inner-point" id="inner-pt-151.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(61.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(63,50,50)"
                                                class="inner-point" id="inner-pt-153"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(63,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(64.5,50,50)"
                                                class="inner-point" id="inner-pt-154.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(64.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(66,50,50)"
                                                class="inner-point" id="inner-pt-156"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(66,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(67.5,50,50)"
                                                class="inner-point" id="inner-pt-157.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(67.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-157.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(69,50,50)"
                                                class="inner-point" id="inner-pt-159"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(69,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(70.5,50,50)"
                                                class="inner-point" id="inner-pt-160.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(70.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(72,50,50)"
                                                class="inner-point" id="inner-pt-162"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(72,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(73.5,50,50)"
                                                class="inner-point" id="inner-pt-163.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(73.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6" fill="#fff" transform="rotate(75,50,50)"
                                                class="inner-point gr-dot" id="inner-pt-165"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(75,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-165">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(74,50,50)" class="hotspot-click-area ripple
                          emptysource
                          emptysourcemobile" height="11.1px" width="7.1px" id="dash-click-165" data-hour="11"
                                                data-quotes="" data-buttontext="" data-cta-url="" data-video-src=""
                                                data-video-src-mobile="">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(76.5,50,50)"
                                                class="inner-point" id="inner-pt-166.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(76.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(78,50,50)"
                                                class="inner-point" id="inner-pt-168"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(78,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(79.5,50,50)"
                                                class="inner-point" id="inner-pt-169.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(79.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(81,50,50)"
                                                class="inner-point" id="inner-pt-171"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(81,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(82.5,50,50)"
                                                class="inner-point" id="inner-pt-172.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(82.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-172.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(84,50,50)"
                                                class="inner-point" id="inner-pt-174"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(84,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(85.5,50,50)"
                                                class="inner-point" id="inner-pt-175.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(85.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(87,50,50)"
                                                class="inner-point" id="inner-pt-177"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(87,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)" transform="rotate(88.5,50,50)"
                                                class="inner-point" id="inner-pt-178.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(88.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="1.3" fill="#ccc" transform="rotate(90,50,50)"
                                                class="inner-point base-fill gr-dot main-dot" id="inner-pt-180">
                                            </circle>
                                            <rect x="49.4" y="-1" fill="rgba(175, 214, 137, 1)" transform="rotate(90,50,50)"
                                                class="hotspot m-hp ripple" height="3.300000000000001px"
                                                width="0.8999999999999999px" id="dash-180"></rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(89,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-180" data-hour="12"
                                                data-quotes="Aligning Cybersecurity with Business Goals"
                                                
                                                data-cta-url="vision-mission.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/video-3.mp4"
                                                data-video-src-mobile="">
                                          
                                          
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)" transform="rotate(91.5,50,50)"
                                                class="inner-point" id="inner-pt-181.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(91.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(93,50,50)"
                                                class="inner-point" id="inner-pt-183"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(93,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(94.5,50,50)"
                                                class="inner-point" id="inner-pt-184.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(94.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(96,50,50)"
                                                class="inner-point" id="inner-pt-186"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(96,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(97.5,50,50)"
                                                class="inner-point" id="inner-pt-187.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(97.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-187.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(99,50,50)"
                                                class="inner-point" id="inner-pt-189"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff" transform="rotate(99,50,50)"
                                                class="inner-dash" height="0.8px" width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(100.5,50,50)"
                                                class="inner-point" id="inner-pt-190.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(100.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(102,50,50)"
                                                class="inner-point" id="inner-pt-192"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(102,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(103.5,50,50)"
                                                class="inner-point" id="inner-pt-193.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(103.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(105,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-195"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(105,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-195">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(104,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-195" data-hour="13"
                                                data-quotes="Leaders with Proven Expertise"
                                                
                                                data-cta-url="founded.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/founded.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/founded.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(106.5,50,50)"
                                                class="inner-point" id="inner-pt-196.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(106.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(108,50,50)"
                                                class="inner-point" id="inner-pt-198"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(108,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(109.5,50,50)"
                                                class="inner-point" id="inner-pt-199.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(109.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(111,50,50)"
                                                class="inner-point" id="inner-pt-201"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(111,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(112.5,50,50)"
                                                class="inner-point" id="inner-pt-202.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(112.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-202.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(114,50,50)"
                                                class="inner-point" id="inner-pt-204"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(114,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(115.5,50,50)"
                                                class="inner-point" id="inner-pt-205.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(115.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(117,50,50)"
                                                class="inner-point" id="inner-pt-207"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(117,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(118.5,50,50)"
                                                class="inner-point" id="inner-pt-208.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(118.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(120,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-210"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(120,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-210">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(119,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-210" data-hour="14"
                                                data-quotes="Agile"
                                                
                                                data-cta-url="agile.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/agile.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/agile.mp4">
                                         
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(121.5,50,50)"
                                                class="inner-point" id="inner-pt-211.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(121.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(123,50,50)"
                                                class="inner-point" id="inner-pt-213"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(123,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(124.5,50,50)"
                                                class="inner-point" id="inner-pt-214.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(124.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(126,50,50)"
                                                class="inner-point" id="inner-pt-216"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(126,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(127.5,50,50)"
                                                class="inner-point" id="inner-pt-217.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(127.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-217.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(129,50,50)"
                                                class="inner-point" id="inner-pt-219"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(129,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(130.5,50,50)"
                                                class="inner-point" id="inner-pt-220.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(130.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(132,50,50)"
                                                class="inner-point" id="inner-pt-222"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(132,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(133.5,50,50)"
                                                class="inner-point" id="inner-pt-223.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(133.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(135,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-225"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(135,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-225">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(134,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-225" data-hour="15"
                                                data-quotes="Real-World Solutions for Real-World Challenges"
                                                
                                                data-cta-url="#"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/practical.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/practical.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(136.5,50,50)"
                                                class="inner-point" id="inner-pt-226.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(136.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(138,50,50)"
                                                class="inner-point" id="inner-pt-228"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(138,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(139.5,50,50)"
                                                class="inner-point" id="inner-pt-229.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(139.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(141,50,50)"
                                                class="inner-point" id="inner-pt-231"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(141,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(142.5,50,50)"
                                                class="inner-point" id="inner-pt-232.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(142.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-232.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(144,50,50)"
                                                class="inner-point" id="inner-pt-234"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(144,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(145.5,50,50)"
                                                class="inner-point" id="inner-pt-235.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(145.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(147,50,50)"
                                                class="inner-point" id="inner-pt-237"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(147,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(148.5,50,50)"
                                                class="inner-point" id="inner-pt-238.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(148.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(150,50,50)" class="inner-point gr-dot"
                                             id="inner-pt-240"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(150,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-240">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(149,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-240" data-hour="16"
                                                data-quotes="Vision and Mission"
                                                
                                               data-cta-url="vision-mission.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/video-3.mp4"
                                                data-video-src-mobile="">
                                          
                                         
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(151.5,50,50)"
                                                class="inner-point" id="inner-pt-241.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(151.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(153,50,50)"
                                                class="inner-point" id="inner-pt-243"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(153,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(154.5,50,50)"
                                                class="inner-point" id="inner-pt-244.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(154.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(156,50,50)"
                                                class="inner-point" id="inner-pt-246"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(156,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(157.5,50,50)"
                                                class="inner-point" id="inner-pt-247.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(157.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-247.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(159,50,50)"
                                                class="inner-point" id="inner-pt-249"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(159,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(160.5,50,50)"
                                                class="inner-point" id="inner-pt-250.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(160.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(162,50,50)"
                                                class="inner-point" id="inner-pt-252"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(162,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(163.5,50,50)"
                                                class="inner-point" id="inner-pt-253.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(163.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(165,50,50)" class="inner-point gr-dot "
                                                id="inner-pt-255"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(165,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-255">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(164,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-255" data-hour="17"
                                                data-quotes="Founded by Industry
Stalwarts"
                                                
                                                data-cta-url="founded.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/founded.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/founded.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(166.5,50,50)"
                                                class="inner-point" id="inner-pt-256.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(166.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(168,50,50)"
                                                class="inner-point" id="inner-pt-258"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(168,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(169.5,50,50)"
                                                class="inner-point" id="inner-pt-259.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(169.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(171,50,50)"
                                                class="inner-point" id="inner-pt-261"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(171,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(172.5,50,50)"
                                                class="inner-point" id="inner-pt-262.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(172.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-262.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(174,50,50)"
                                                class="inner-point" id="inner-pt-264"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(174,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(175.5,50,50)"
                                                class="inner-point" id="inner-pt-265.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(175.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(177,50,50)"
                                                class="inner-point" id="inner-pt-267"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(177,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(178.5,50,50)"
                                                class="inner-point" id="inner-pt-268.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(178.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="1.3" fill="#ccc" transform="rotate(180,50,50)"
                                                class="inner-point base-fill gr-dot main-dot" id="inner-pt-270">
                                            </circle>
                                            <rect x="49.4" y="-1" fill="rgba(175, 214, 137, 1)" transform="rotate(180,50,50)"
                                                class="hotspot m-hp ripple" height="3.300000000000001px"
                                                width="0.8999999999999999px" id="dash-270"></rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(179,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-270" data-hour="18"
                                                data-quotes="Driving Innovation, Inspiring Security"
                                                
                                                data-cta-url="flexible.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/flexible-2.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/flexible-2.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)"
                                                transform="rotate(181.5,50,50)" class="inner-point" id="inner-pt-271.5">
                                            </circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(181.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(183,50,50)"
                                                class="inner-point" id="inner-pt-273"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(183,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(184.5,50,50)"
                                                class="inner-point" id="inner-pt-274.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(184.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(186,50,50)"
                                                class="inner-point" id="inner-pt-276"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(186,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(187.5,50,50)"
                                                class="inner-point" id="inner-pt-277.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(187.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-277.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(189,50,50)"
                                                class="inner-point" id="inner-pt-279"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(189,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(190.5,50,50)"
                                                class="inner-point" id="inner-pt-280.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(190.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(192,50,50)"
                                                class="inner-point" id="inner-pt-282"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(192,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(193.5,50,50)"
                                                class="inner-point" id="inner-pt-283.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(193.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(195,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-285"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(195,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-285">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(194,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-285" data-hour="19"
                                                  data-quotes="Industry
Experience"
                                                
                                              data-cta-url="industry.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/practical.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/practical.mp4">
                                           
                                           
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(196.5,50,50)"
                                                class="inner-point" id="inner-pt-286.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(196.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(198,50,50)"
                                                class="inner-point" id="inner-pt-288"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(198,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(199.5,50,50)"
                                                class="inner-point" id="inner-pt-289.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(199.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(201,50,50)"
                                                class="inner-point" id="inner-pt-291"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(201,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(202.5,50,50)"
                                                class="inner-point" id="inner-pt-292.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(202.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-292.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(204,50,50)"
                                                class="inner-point" id="inner-pt-294"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(204,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(205.5,50,50)"
                                                class="inner-point" id="inner-pt-295.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(205.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(207,50,50)"
                                                class="inner-point" id="inner-pt-297"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(207,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(208.5,50,50)"
                                                class="inner-point" id="inner-pt-298.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(208.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(210,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-300"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(210,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-300">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(209,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-300" data-hour="20"
                                                data-quotes="Practical"
                                                
                                                data-cta-url="practical.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/video-3.mp4"
                                                data-video-src-mobile="">
                                          
                                         
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(211.5,50,50)"
                                                class="inner-point" id="inner-pt-301.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(211.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(213,50,50)"
                                                class="inner-point" id="inner-pt-303"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(213,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(214.5,50,50)"
                                                class="inner-point" id="inner-pt-304.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(214.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(216,50,50)"
                                                class="inner-point" id="inner-pt-306"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(216,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(217.5,50,50)"
                                                class="inner-point" id="inner-pt-307.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(217.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-307.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(219,50,50)"
                                                class="inner-point" id="inner-pt-309"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(219,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(220.5,50,50)"
                                                class="inner-point" id="inner-pt-310.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(220.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(222,50,50)"
                                                class="inner-point" id="inner-pt-312"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(222,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(223.5,50,50)"
                                                class="inner-point" id="inner-pt-313.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(223.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(225,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-315"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(225,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-315">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(224,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-315" data-hour="21"
                                                data-quotes="Building on Strong Foundations" 
                                                data-cta-url="fundamentals.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/flexible-2.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/flexible-2.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(226.5,50,50)"
                                                class="inner-point" id="inner-pt-316.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(226.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(228,50,50)"
                                                class="inner-point" id="inner-pt-318"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(228,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(229.5,50,50)"
                                                class="inner-point" id="inner-pt-319.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(229.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(231,50,50)"
                                                class="inner-point" id="inner-pt-321"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(231,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(232.5,50,50)"
                                                class="inner-point" id="inner-pt-322.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(232.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-322.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(234,50,50)"
                                                class="inner-point" id="inner-pt-324"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(234,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(235.5,50,50)"
                                                class="inner-point" id="inner-pt-325.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(235.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(237,50,50)"
                                                class="inner-point" id="inner-pt-327"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(237,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(238.5,50,50)"
                                                class="inner-point" id="inner-pt-328.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(238.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(240,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-330"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(240,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-330">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(239,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-330" data-hour="22"
                                                   data-quotes="Business
Understanding"
                                                
                                                data-cta-url="business-understanding.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/video-1.mp4"
                                                data-video-src-mobile="#">
                                        
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(241.5,50,50)"
                                                class="inner-point" id="inner-pt-331.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(241.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(243,50,50)"
                                                class="inner-point" id="inner-pt-333"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(243,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(244.5,50,50)"
                                                class="inner-point" id="inner-pt-334.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(244.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(246,50,50)"
                                                class="inner-point" id="inner-pt-336"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(246,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(247.5,50,50)"
                                                class="inner-point" id="inner-pt-337.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(247.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-337.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(249,50,50)"
                                                class="inner-point" id="inner-pt-339"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(249,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(250.5,50,50)"
                                                class="inner-point" id="inner-pt-340.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(250.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(252,50,50)"
                                                class="inner-point" id="inner-pt-342"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(252,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(253.5,50,50)"
                                                class="inner-point" id="inner-pt-343.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(253.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(255,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-345"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(255,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-345">
                                            </rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(254,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-345" data-hour="23"
                                                    data-quotes="Based on Fundamentals" 
                                                data-cta-url="fundamentals.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/flexible.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/flexible.mp4">
                                           
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(256.5,50,50)"
                                                class="inner-point" id="inner-pt-346.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(256.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(258,50,50)"
                                                class="inner-point" id="inner-pt-348"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(258,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(259.5,50,50)"
                                                class="inner-point" id="inner-pt-349.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(259.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(261,50,50)"
                                                class="inner-point" id="inner-pt-351"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(261,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(262.5,50,50)"
                                                class="inner-point" id="inner-pt-352.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(262.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-352.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(264,50,50)"
                                                class="inner-point" id="inner-pt-354"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(264,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(265.5,50,50)"
                                                class="inner-point" id="inner-pt-355.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(265.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(267,50,50)"
                                                class="inner-point" id="inner-pt-357"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(267,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(268.5,50,50)"
                                                class="inner-point" id="inner-pt-358.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(268.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="1.3" fill="#ccc" transform="rotate(270,50,50)"
                                                class="inner-point base-fill gr-dot main-dot" id="inner-pt-0"></circle>
                                            <rect x="49.4" y="-1" fill="rgba(175, 214, 137, 1)" transform="rotate(270,50,50)"
                                                class="hotspot m-hp ripple" height="3.300000000000001px"
                                                width="0.8999999999999999px" id="dash-0"></rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(269,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-0" data-hour="0"
                                                data-quotes="Decades of Proven Industry Expertise"
                                                
                                                data-cta-url="industry.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/practical.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/practical.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)"
                                                transform="rotate(271.5,50,50)" class="inner-point" id="inner-pt-1.5">
                                            </circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(271.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(273,50,50)"
                                                class="inner-point" id="inner-pt-3"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(273,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(274.5,50,50)"
                                                class="inner-point" id="inner-pt-4.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(274.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(276,50,50)"
                                                class="inner-point" id="inner-pt-6"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(276,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(277.5,50,50)"
                                                class="inner-point" id="inner-pt-7.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(277.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-7.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(279,50,50)"
                                                class="inner-point" id="inner-pt-9"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(279,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(280.5,50,50)"
                                                class="inner-point" id="inner-pt-10.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(280.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(282,50,50)"
                                                class="inner-point" id="inner-pt-12"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(282,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(283.5,50,50)"
                                                class="inner-point" id="inner-pt-13.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(283.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(285,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-15"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(285,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-15"></rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(284,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-15" data-hour="1"
                                                data-quotes="Aligning Cybersecurity with Business Goals"
                                                
                                                data-cta-url="vision-mission.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/video-3.mp4"
                                                data-video-src-mobile="">
                                          
                                          
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(286.5,50,50)"
                                                class="inner-point" id="inner-pt-16.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(286.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(288,50,50)"
                                                class="inner-point" id="inner-pt-18"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(288,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(289.5,50,50)"
                                                class="inner-point" id="inner-pt-19.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(289.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(291,50,50)"
                                                class="inner-point" id="inner-pt-21"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(291,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(292.5,50,50)"
                                                class="inner-point" id="inner-pt-22.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(292.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-22.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(294,50,50)"
                                                class="inner-point" id="inner-pt-24"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(294,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(295.5,50,50)"
                                                class="inner-point" id="inner-pt-25.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(295.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(297,50,50)"
                                                class="inner-point" id="inner-pt-27"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(297,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(298.5,50,50)"
                                                class="inner-point" id="inner-pt-28.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(298.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(300,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-30"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(300,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-30"></rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(299,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-30" data-hour="2"
                                                data-quotes="Tailored Solutions for Every Need" 
                                                data-cta-url="flexible.html"
                                                 data-video-src="https://trogon.info/website/cs-new/images-2/new/video-2.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/video-2.mp4">
                                            
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(301.5,50,50)"
                                                class="inner-point" id="inner-pt-31.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(301.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(303,50,50)"
                                                class="inner-point" id="inner-pt-33"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(303,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(304.5,50,50)"
                                                class="inner-point" id="inner-pt-34.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(304.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(306,50,50)"
                                                class="inner-point" id="inner-pt-36"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(306,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(307.5,50,50)"
                                                class="inner-point" id="inner-pt-37.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(307.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-37.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(309,50,50)"
                                                class="inner-point" id="inner-pt-39"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(309,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(310.5,50,50)"
                                                class="inner-point" id="inner-pt-40.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(310.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(312,50,50)"
                                                class="inner-point" id="inner-pt-42"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(312,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(313.5,50,50)"
                                                class="inner-point" id="inner-pt-43.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(313.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(315,50,50)" class="inner-point gr-dot "
                                                id="inner-pt-45"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(315,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-45"></rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(314,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-45" data-hour="3"
                                                data-quotes="A Comprehensive Approach to Cybersecurity"
                                                
                                                data-cta-url="practical.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/comprohensive.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/comprohensive.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(316.5,50,50)"
                                                class="inner-point" id="inner-pt-46.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(316.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(318,50,50)"
                                                class="inner-point" id="inner-pt-48"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(318,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(319.5,50,50)"
                                                class="inner-point" id="inner-pt-49.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(319.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(321,50,50)"
                                                class="inner-point" id="inner-pt-51"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(321,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(322.5,50,50)"
                                                class="inner-point" id="inner-pt-52.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(322.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-52.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(324,50,50)"
                                                class="inner-point" id="inner-pt-54"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(324,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(325.5,50,50)"
                                                class="inner-point" id="inner-pt-55.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(325.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(327,50,50)"
                                                class="inner-point" id="inner-pt-57"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(327,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(328.5,50,50)"
                                                class="inner-point" id="inner-pt-58.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(328.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(330,50,50)" class="inner-point gr-dot "
                                                id="inner-pt-60"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(330,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-60"></rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(329,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-60" data-hour="4"
                            data-quotes="Experts You Can Trust, Results You Can Measure"
                                                
                                                data-cta-url="practical.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/founded.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/founded.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(331.5,50,50)"
                                                class="inner-point" id="inner-pt-61.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(331.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(333,50,50)"
                                                class="inner-point" id="inner-pt-63"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(333,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(334.5,50,50)"
                                                class="inner-point" id="inner-pt-64.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(334.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(336,50,50)"
                                                class="inner-point" id="inner-pt-66"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(336,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(337.5,50,50)"
                                                class="inner-point" id="inner-pt-67.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(337.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-67.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(339,50,50)"
                                                class="inner-point" id="inner-pt-69"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(339,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(340.5,50,50)"
                                                class="inner-point" id="inner-pt-70.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(340.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(342,50,50)"
                                                class="inner-point" id="inner-pt-72"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(342,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(343.5,50,50)"
                                                class="inner-point" id="inner-pt-73.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(343.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.6000000000000001" fill="#ccc"
                                                transform="rotate(345,50,50)" class="inner-point gr-dot"
                                                id="inner-pt-75"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(345,50,50)"
                                                class="hotspot ripple" height="2.1px" width="0.3px" id="dash-75"></rect>
                                            <rect x="49" y="0" fill="rgba(0,0,0,1)" transform="rotate(344,50,50)" class="hotspot-click-area ripple
                          
                          " height="11.1px" width="7.1px" id="dash-click-75" data-hour="5"
                                                data-quotes="Holistic"
                                                
                                                data-cta-url="holistic.html"
                                                data-video-src="https://trogon.info/website/cs-new/images-2/new/agile.mp4"
                                                data-video-src-mobile="https://trogon.info/website/cs-new/images-2/new/agile.mp4">
                                            </rect>

                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(346.5,50,50)"
                                                class="inner-point" id="inner-pt-76.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(346.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(348,50,50)"
                                                class="inner-point" id="inner-pt-78"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(348,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(349.5,50,50)"
                                                class="inner-point" id="inner-pt-79.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(349.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(351,50,50)"
                                                class="inner-point" id="inner-pt-81"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(351,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(352.5,50,50)"
                                                class="inner-point" id="inner-pt-82.5"></circle>
                                            <rect x="50" y="1" fill="#fff" transform="rotate(352.5,50,50)"
                                                class="hotspot" height="2.1px" width="0.3px" id="dash-82.5"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(354,50,50)"
                                                class="inner-point" id="inner-pt-84"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(354,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(355.5,50,50)"
                                                class="inner-point" id="inner-pt-85.5"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(355.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="#fff" transform="rotate(357,50,50)"
                                                class="inner-point" id="inner-pt-87"></circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(357,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle cx="50" cy="4" r="0.1" fill="rgba(175, 214, 137, 1)"
                                                transform="rotate(358.5,50,50)" class="inner-point" id="inner-pt-88.5">
                                            </circle>
                                            <rect x="50" y="1.7999999999999998" fill="#fff"
                                                transform="rotate(358.5,50,50)" class="inner-dash" height="0.8px"
                                                width="0.2px"></rect>
                                            <circle id="hotspot-pointer" class="hotspot-pointer-dot" cx="50" cy="4"
                                                r="1.6" fill="rgba(175, 214, 137, 1)"></circle>
                                        </svg>

                                        <div id="dial-content" class="content-dial no-display">
                                            <div id="mob-hotspot-text" class="mobile-hotspot-text">
                                                <span></span>
                                            </div>
                                            <h2 id="dial-content-text" class="content-dial-txt"></h2>
                                            <div class="explore-btn">
                                                <a id="explore"
                                                    href="#"
                                                    class="button-explore no-display" style="display: inline-block;">
                                                    Know More
                                                </a>
                                            </div>
                                        </div>
                                        <div id="controls-container">
                                            <!-- Next Controller -->
                                            <button aria-label="Next" id="next-controller">

                                                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40"
                                                    viewBox="0 0 40 40" fill="none">
                                                    <path
                                                        d="M21.35 13.2333L25.7167 17.5999C27 18.8833 27 20.9833 25.7167 22.2666L14.85 33.1333"
                                                        stroke="white" stroke-width="2.5" stroke-miterlimit="10"
                                                        stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M14.85 6.7334L16.5833 8.46673" stroke="white"
                                                        stroke-width="2.5" stroke-miterlimit="10" stroke-linecap="round"
                                                        stroke-linejoin="round"></path>
                                                </svg>

                                            </button>

                                            <button aria-label="Previous" id="prev-controller">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40"
                                                    viewBox="0 0 40 40" fill="none">
                                                    <path
                                                        d="M18.65 13.2333L14.2833 17.5999C13 18.8833 13 20.9833 14.2833 22.2666L25.15 33.1333"
                                                        stroke="white" stroke-width="2.5" stroke-miterlimit="10"
                                                        stroke-linecap="round" stroke-linejoin="round"></path>
                                                    <path d="M25.15 6.7334L23.4167 8.46673" stroke="white"
                                                        stroke-width="2.5" stroke-miterlimit="10" stroke-linecap="round"
                                                        stroke-linejoin="round"></path>
                                                </svg>
                                            </button>

                                           
                                        </div>
                                    </div>
                                </div>
                                <!--<div id="video-controls" class="no-display">-->
                                <!--    <div class="vid-controls">-->
                                <!--        <div class="video-play">-->
                                <!--            <button class="play-btn" id="play-pause"></button>-->
                                <!--        </div>-->
                                <!--        <div class="vid-progress">-->
                                <!--            <meter id="video-meter" class="vid-meter" value="100" min="0"-->
                                <!--                max="100"></meter>-->
                                <!--        </div>-->
                                <!--        <div class="equalizer">-->
                                <!--           ///////////-->
                                <!--        </div>-->
                                <!--    </div>-->
                                <!--</div>-->
                            </div>
                        </div>




                        <script>
                            $(document).ready(function () {

                                var isPlaying = true; // Variable to track the playback state

                                // Define SVG icons
                                var playIcon = `
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
  <path d="M28.55 13.3C34.9333 16.9834 34.9333 23.0167 28.55 26.7L23.4 29.6667L18.25 32.6334C11.8833 36.3167 6.66667 33.3 6.66667 25.9334V20V14.0667C6.66667 6.70003 11.8833 3.68336 18.2667 7.3667L22.0167 9.53336" stroke="white" stroke-width="2.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `;

                                var pauseIcon = `
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
                        <path d="M8.35 5C5.95 5 5 5.9 5 8.15V31.85C5 34.1 5.95 35 8.35 35H14.4C16.7833 35 17.75 34.1 17.75 31.85V8.15C17.75 5.9 16.8 5 14.4 5" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M31.65 35C34.0333 35 35 34.1 35 31.85V8.15C35 5.9 34.05 5 31.65 5H25.6C23.2167 5 22.25 5.9 22.25 8.15V31.85C22.25 34.1 23.2 35 25.6 35" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `;

                                $('#play-controller').html(pauseIcon);

                                // Function to toggle play/pause
                                function togglePlayPause() {
                                    if (isPlaying) {
                                        stopCurrentVideo();
                                        $('#play-controller').html(playIcon); // Change button to play icon
                                    } else {
                                        playNextVideo();
                                        $('#play-controller').html(pauseIcon); // Change button to pause icon
                                    }
                                    isPlaying = !isPlaying; // Toggle the playback state
                                }

                                // Attach event listener for play/pause controller
                                $('#play-controller').click(function () {
                                    togglePlayPause();
                                });


                                // Function to play the next video
                                function playNextVideo() {
                                    var currentIndex = parseInt($(hotspotPointer).attr("data-current-index"));
                                    var nextIndex = (currentIndex + 1) % 24; // Assuming there are 24 elements
                                    $("#dash-click-" + (nextIndex * 15)).click();
                                }

                                // Function to play the previous video
                                function playPreviousVideo() {
                                    var currentIndex = parseInt($(hotspotPointer).attr("data-current-index"));
                                    var previousIndex = (currentIndex - 1 + 24) % 24; // Assuming there are 24 elements
                                    $("#dash-click-" + (previousIndex * 15)).click();
                                }

                                // Function to stop the current video
                                function stopCurrentVideo() {
                                    // Implement logic to stop the current video playback
                                    // For example, you can pause the video and reset its source
                                    videoEven.pause();
                                    videoEven.src = '';
                                    videoEven.load();
                                    videoOdd.pause();
                                    videoOdd.src = '';
                                    videoOdd.load();
                                    // You may need additional logic here based on your requirements
                                }

                                // Attach event listener for next controller
                                $('#next-controller').click(function () {
                                    playNextVideo();
                                });

                                // Attach event listener for previous controller
                                $('#prev-controller').click(function () {
                                    playPreviousVideo();
                                });

                            });
                        </script>

                    </div>
                </div>
                <div class="scroll-indicator">
                    <span class="arrow"></span>
                </div>
            </div>
        </div>

            

<!--<div class="banner-container">-->
<!--        <div class="main-container">-->
<!--            <div class="circular-component">-->
                <!-- Central Content -->
<!--                <div class="central-content">-->
<!--                    <div class="content-inner" id="contentInner">-->
<!--                        <h1 class="main-title" id="mainTitle">Aligning</h1>-->
<!--                        <h2 class="subtitle-line" id="subtitle1">Cybersecurity with</h2>-->
<!--                        <h2 class="subtitle-line" id="subtitle2">Business Goals</h2>-->
<!--                        <button class="cta-button" id="ctaButton">-->
<!--                            <span id="buttonText">Know More</span>-->
<!--                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">-->
<!--                                <polyline points="9,18 15,12 9,6"></polyline>-->
<!--                            </svg>-->
<!--                        </button>-->
<!--                    </div>-->
<!--                </div>-->

                <!-- SVG Circle -->
<!--                <div class="svg-container">-->
<!--                    <svg class="circular-svg" viewBox="0 0 600 600">-->
                        <!-- Outer circle border with dashes -->
<!--                        <circle cx="300" cy="300" r="250" class="circle-border"/>-->
                        
                        <!-- Inner circle border with dashes -->
<!--                        <circle cx="300" cy="300" r="230" class="circle-border"/>-->
                        
                        <!-- Progress ring on inner circle -->
<!--                        <circle id="progressRing" cx="300" cy="300" r="230" class="progress-ring" -->
<!--                                transform="rotate(-90 300 300)" stroke-dasharray="0 1445"/>-->
                        
                        <!-- Interactive elements container -->
<!--                        <g id="pointsContainer"></g>-->
<!--                    </svg>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->










     <section class="gallery-one section-space">
    <div class="container">
        <div class="row gutter-y-40 align-items-center">
                            <div class="col-lg-6">
                                <div class="sec-title">

                                    <h6 class="sec-title__tagline @@extraClassName">WHAT WE DO</h6>

                                    <h3 class="sec-title__title">What We Do to  <span class='sec-title__title__inner'>Secure</span> Your Digital World</h3>
                                </div>
                            </div>
                        
                        </div>
        <div class="gallery-one__top cleenhearts-owl__carousel cleenhearts-owl__carousel--basic-nav owl-carousel owl-theme owl-loaded owl-drag" 
            data-owl-options="{
                &quot;items&quot;: 1,
                &quot;margin&quot;: 5,
                &quot;loop&quot;: true,
                &quot;smartSpeed&quot;: 700,
                &quot;animateOut&quot;: &quot;fadeOut&quot;,
                &quot;nav&quot;: true,
                &quot;navText&quot;: [&quot;<span class=\&quot;icon-arrow-left\&quot;></span>&quot;,&quot;<span class=\&quot;icon-arrow-right\&quot;></span>&quot;],
                &quot;dots&quot;: false,
                &quot;autoplay&quot;: false,
                &quot;autoplayTimeout&quot;: 5000
            }">
            
            <div class="owl-stage-outer">
                <div class="owl-stage" style="transform: translate3d(-3525px, 0px, 0px); transition: all; width: 12925px;">

                    
                    <div class="owl-item active" style="width: 1170px; margin-right: 5px;">
                        <div class="item">
                            <div class="gallery-one__top__image" style="background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, .3), rgba(0, 0, 0, 0.3)),url('images-2/new/detective-evaluating-old-archived-folders-organize-all-cases-based-crimes.webp');">
                                <div class="content-wrapper">
                                    <h2>Professional Services</h2>
                                    <p>Our expert team provides tailored cybersecurity strategies, risk assessments, and compliance consulting to strengthen your organization’s defenses. We deliver professional guidance to protect your critical assets and ensure operational resilience.</p>
                                    <a href="professional-services.html" class="btn btn-primary">Learn More</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Other Items -->
                    <div class="owl-item" style="width: 1170px; margin-right: 5px;">
                        <div class="item">
                            <div class="gallery-one__top__image" style="background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, .3), rgba(0, 0, 0, 0.3)),url('images-2/new/multiethnic-colleagues-overseeing-supercomputers-data-center.webp');">
                                <div class="content-wrapper">
                                    <h2>Managed Services</h2>
                                    <p>Stay ahead of cyber threats with our proactive monitoring, incident response, and continuous threat intelligence. Our managed services ensure your systems are secure 24/7, minimizing downtime and maximizing peace of mind.</p>
                                    <a href="managed-services.html" class="btn btn-primary">Learn More</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="owl-item" style="width: 1170px; margin-right: 5px;">
                        <div class="item">
                            <div class="gallery-one__top__image" style="background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, .3), rgba(0, 0, 0, 0.3)),url('images-2/new/cyber-security-concept-digital-art.webp');">
                                <div class="content-wrapper">
                                    <h2>Solutions</h2>
                                    <p>We offer cutting-edge solutions, including advanced threat detection, cloud security, and endpoint protection, designed to safeguard your digital infrastructure and keep your business running smoothly.</p>
                                    <a href="solutions.html" class="btn btn-primary">Learn More</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                     <div class="owl-item" style="width: 1170px; margin-right: 5px;">
                        <div class="item">
                            <div class="gallery-one__top__image" style="background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, .3), rgba(0, 0, 0, 0.3)),url('images-2/new/business-teammates-working-late.webp');">
                                <div class="content-wrapper">
                                    <h2>Buzz</h2>
                                    <p>Stay informed with the latest updates, trends, and insights in the world of cybersecurity. Our Buzz section keeps you connected with emerging technologies, industry news, and expert tips to stay ahead of evolving threats.</p>
                                    <a href="ai-security.html" class="btn btn-primary">Learn More</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

      
            <div class="owl-nav">
                <button type="button" role="presentation" class="owl-prev" aria-label="carousel button">
                    <span class="icon-arrow-left"></span>
                </button>
                <button type="button" role="presentation" class="owl-next" aria-label="carousel button">
                    <span class="icon-arrow-right"></span>
                </button>
            </div>

  
            <div class="owl-dots disabled"></div>
        </div>
    </div>
</section>

     <section class="about-three">
       <div class="container">
    <div class="row gutter-y-50">
                <div class="col-lg-6">
            <div class="about-three__content">
                <div class="sec-title">
                    <h6 class="sec-title__tagline @@extraClassName">What We Focus On</h6>
                    <h3 class="sec-title__title">
Best Solutions for  <span class="sec-title__title__inner">a Secure Digital World</span></h3>
                </div>
                <p class="about-three__text about-three__text--one">We offer futuristic cybersecurity solutions and services that are cost-effective and customer friendly.Dedicated project management team to ensure Quality Assurance, Risk Management, Cost Optimization, Deliverables, Remediation Measures & Reports at every stage.We are committed to ensuring that project activities are completed in an innovative and holistic manner, with the dedicated engagement of all team members to achieve the project's goals and objectives.Our team comprises experienced cybersecurity professionals with real-world expertise in offensive and defensive strategies, investigative techniques, and leadership within the cybersecurity domain. Together, we work to provide comprehensive solutions to our clients.</p>

            </div>
        </div>
        <div class="col-lg-6">
            <div class="about-three__image">
                <div class=" wow fadeInLeft animated" data-wow-duration="1500ms" data-wow-delay="00ms" style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInLeft;">
                    <img src="images-2/new/securityr.png" class="img-fluid">
                   
            
                </div>
            </div>
        </div>

    </div>
</div>

        </section>

<section class="counter-wrapper mt-5">
    <div class="counter-inner">
        <div class="container">
            <div class="row aos-init aos-animate" data-aos="fade-left" data-aos-easing="linear" data-aos-duration="1000">
                <div class="col-12 col-sm-6 col-md-3">
                    <div class="py-4 text-center text-white">
                        <div class="py-2">
                            <span id="count1" class="count_number">27+</span>
                        </div>
                        <div>Years of Experience</div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                    <div class="py-4 text-center text-white">
                        <div class="py-2">
                            <span id="count2" class="count_number">250+</span>
                        </div>
                        <div>Clients Served</div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                    <div class="py-4 text-center text-white">
                        <div class="py-2">
                            <span id="count3" class="count_number">500+</span>
                        </div>
                        <div>Projects Completed</div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                    <div class="py-4 text-center text-white">
                        <div class="py-2">
                            <span id="count4" class="count_number">150+</span>
                        </div>
                        <div>Professionals</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
       <section class="team-one section-space">
    <div class="container">
        <div class="team-one__top">
            <div class="row gutter-y-30 align-items-center">
                <div class="col-xxl-8 col-lg-7">
                    <div class="sec-title">
                        <h6 class="sec-title__tagline @@extraClassName">Our expert team</h6>
                        <h3 class="sec-title__title">Meet The Team Behind Our <span class="sec-title__title__inner">Success</span> Story</h3>
                    </div>
                </div>
              
            </div>
        </div>
        <div class="team-one__carousel cleenhearts-owl__carousel cleenhearts-owl__carousel--with-shadow cleenhearts-owl__carousel--basic-nav owl-theme owl-carousel" data-owl-options='{
        "items": 3,
        "margin": 30,
        "smartSpeed": 700,
        "loop":true,
        "autoplay": 6000,
        "nav":true,
        "dots":false,
        "navText": ["<span class=\"icon-arrow-left\"></span>","<span class=\"icon-arrow-right\"></span>"],
        "responsive":{
            "0":{
                "items": 1,
                "margin": 20
            },
            "575":{
                "items": 1,
                "margin": 30
            },
            "768":{
                "items": 2,
                "margin": 30
            },
            "992":{
                "items": 3,
                "margin": 30
            },
            "1200":{
                "items": 3,
                "margin": 30
            }
        }
        }'>
            <div class="item">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/ILLYAS.jpeg" alt="Illyas Kooliyankal">
                        <div class="team-single__content">
                            <div class="team-single__content__inner">
                                <h4 class="team-single__name">Illyas Kooliyankal</h4>
                                <p class="team-single__designation">Cybersecurity Advisor & Group CEO</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="item">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/shafeer.jpeg" alt="#">
                        <div class="team-single__content">
                            <div class="team-single__content__inner">
                                <h4 class="team-single__name">Mohammed Sheefar
Ibrahim</h4>
                                <p class="team-single__designation">CTO & Chief of Staff</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="item">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/SAJJAD.jpeg" alt="Ashique Sajjad">
                        <div class="team-single__content">
                          <div class="team-single__content__inner">
                                <h4 class="team-single__name">Ashique Sajjad</h4>
                                <p class="team-single__designation">Chief Operating Officer (COO)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="item">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/NASHATH.jpeg" alt="Illyas Kooliyankal">
                        <div class="team-single__content">
                          <div class="team-single__content__inner">
                                <h4 class="team-single__name">Mohammed Nashath </h4>
                                <p class="team-single__designation">Head of Cybersecurity services</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="item d-none">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/SINAN.jpeg" alt="Patricia E. Wall">
                        <div class="team-single__content">
                            <div class="team-single__content__inner">
                                <h4 class="team-single__name">
Sinan Abdulla</h4>
                                <p class="team-single__designation">Cybersecurity Sales Engineer</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="item">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/SHAZIYA.jpeg" alt="Ashique Sajjad">
                        <div class="team-single__content">
                            <div class="team-single__content__inner">
                                <h4 class="team-single__name">Shaziya Mahamood</h4>
                                <p class="team-single__designation">Manager- Cybersecurity Services</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                        <div class="item">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/AAFREEN.jpeg" alt="Ashique Sajjad">
                        <div class="team-single__content">
                            <div class="team-single__content__inner">
                                <h4 class="team-single__name">Aafreen Felza Faris</h4>
                                <p class="team-single__designation">Manager- Cybersecurity Services</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
             <div class="item">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/SADHIK.jpeg" alt="Ashique Sajjad">
                        <div class="team-single__content">
                            <div class="team-single__content__inner">
                                <h4 class="team-single__name">Muhammed Sadique</h4>
                                <p class="team-single__designation">Finance & Administration Officer</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
                         <div class="item">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/SHAIKH.jpeg" alt="Ashique Sajjad">
                        <div class="team-single__content">
                            <div class="team-single__content__inner">
                                <h4 class="team-single__name">Mohammed Meeran Shaikh</h4>
                                <p class="team-single__designation">Head - Information Security
Engineering & Cloud Security</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                         <div class="item">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/SHARIQ.jpeg" alt="Ashique Sajjad">
                        <div class="team-single__content">
                            <div class="team-single__content__inner">
                                <h4 class="team-single__name">Shariq Shajir</h4>
                                <p class="team-single__designation">Cybersecurity Engineer</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
<div class="item">
                <div class="team-single">
                    <div class="team-single__image">
                        <img src="images-2/new/ADIL.jpeg" alt="Ashique Sajjad">
                        <div class="team-single__content">
                            <div class="team-single__content__inner">
                                <h4 class="team-single__name">Adil Mohammed</h4>
                                <p class="team-single__designation">Client Director - Cybersecurity Excellency</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>
</section>

<div class="blog-one section-space">
    <div class="container">
        <div class="row">
            <div class="col-xl-6 col-lg-7 col-md-9">
                <div class="sec-title">
                    <h6 class="sec-title__tagline @@extraClassName">How We Protect You</h6>
                    <h3 class="sec-title__title">Your 
                    Privacy is  <span class="sec-title__title__inner">Covered </span></h3>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6 mt-3">
                <div class="blog-card wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="400ms">
                    <a href="#" class="blog-card__image">
                        <img src="images-2/futuristic-business-scene-with-ultra-modern-ambiance.webp" alt="Nulla quam neque, inter fermen tum lacinia, inter">
                    </a>
                    <div class="blog-card__content" >
                        <h3 class="blog-card__title"><a href="#">Privacy Protection</a></h3>
                        <p>We provide complete privacy to your data and data stored in your system even when audit you to make yourselves safe when an attack happens.</p>

                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mt-3">
                <div class="blog-card wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="000ms">
                    <a href="#" class="blog-card__image">
                        <img src="images-2/futuristic-cybersecurity-padlock-digital-circuit-board-data-protection-encryption-hightech-network-security-concept_1335075-27079.jpg
" alt="#">
                    </a>
                    <div class="blog-card__content" >
                        <h3 class="blog-card__title"><a href="#">Secure Network</a></h3>
                        <p>Networks are the bridges that connects your machines to internet. We care for you to fix the gaps in your bridges.</p>
                    </div>
                </div>
            </div>
                   <div class="col-lg-4 col-md-6 mt-3">
                <div class="blog-card wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="000ms">
                    <a href="#" class="blog-card__image">
                        <img src="images-2/man-sitting-front-computer-with-padlocks-monitors-background-digital-security_1304780-1213.webp" alt="#">
                    </a>
                    <div class="blog-card__content" >
                        <h3 class="blog-card__title"><a href="#">Malware & Virus Protection</a></h3>
                        <p>We have the best team to protect you from malware and virus attacks. We provide 24/7 support for incident response for our customers.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


      <section class="help-donate-one section-space-top">
          
            <div class="container">
                <div class="sec-title">
                    <h6 class="sec-title__tagline sec-title__tagline--center">OUR WORLD-CLASS CYBER SECURITY OFFERINGS</h6>
                </div>
            </div>
            <div class="help-donate-one__slide">
        <span class="help-donate-one__text help-donate-one__text--one">Data Security , </span>
        <span class="help-donate-one__text help-donate-one__text--two">Network Protection , </span>
        
        <span class="help-donate-one__text help-donate-one__text--one">Endpoint Security , </span>
        <span class="help-donate-one__text help-donate-one__text--two">Cloud Security , </span>
        <span class="help-donate-one__text help-donate-one__text--two">Risk Assessment , </span>
        <span class="help-donate-one__text help-donate-one__text--one">Incident Response , </span>
        <span class="help-donate-one__text help-donate-one__text--two">Compliance , </span>
    </div>
        </section>

<section class="donations-one donations-carousel section-space-bottom">
    <div class="container">
        <div class="donations-one__carousel cleenhearts-owl__carousel cleenhearts-owl__carousel--basic-nav owl-theme owl-carousel owl-loaded owl-drag"
            data-owl-options="{
    &quot;items&quot;: 3,
    &quot;margin&quot;: 30,
    &quot;smartSpeed&quot;: 700,
    &quot;loop&quot;:true,
    &quot;autoplay&quot;: 6000,
    &quot;nav&quot;:true,
    &quot;dots&quot;:false,
    &quot;navText&quot;: [&quot;<span class=\&quot;icon-arrow-left\&quot;></span>&quot;,&quot;<span class=\&quot;icon-arrow-right\&quot;></span>&quot;],
    &quot;responsive&quot;:{
        &quot;0&quot;:{
            &quot;items&quot;: 1,
            &quot;margin&quot;: 20
        },
        &quot;576&quot;:{
            &quot;items&quot;: 1,
            &quot;margin&quot;: 30
        },
        &quot;768&quot;:{
            &quot;items&quot;: 2,
            &quot;margin&quot;: 30
        },
        &quot;992&quot;:{
            &quot;items&quot;: 2,
            &quot;margin&quot;: 30
        },
        &quot;1200&quot;:{
            &quot;items&quot;: 3,
            &quot;margin&quot;: 30
        }
    }
    }">
           
         <div class="owl-stage-outer">
    <div class="owl-stage" style="transform: translate3d(-2000px, 0px, 0px); transition: 0.7s; width: 4800px;">
        <div class="owl-item active" style="width: 370px; margin-right: 30px;">
            <div class="item wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="00ms"
                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                <div class="donation-card @@extraClassName">
                    <div class="donation-card__bg"></div>
                    <a href="#" class="donation-card__image">
                        <img src="images-2/new/cloud-security.webp"
                            alt="Quisque dictum eget accumsan dignissim. Quisque">
                    </a>
                    <div class="donation-card__content">
                        <h3 class="donation-card__title"><a href="#">Cloud Security</a></h3>
                        <p>Trust CyberShelter's innovative cloud security solutions and experienced experts to safeguard your cloud journey, as security controls and assurance are vital decision-making factors.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="owl-item active" style="width: 370px; margin-right: 30px;">
            <div class="item wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="00ms"
                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                <div class="donation-card @@extraClassName">
                    <div class="donation-card__bg"></div>
                    <a href="#" class="donation-card__image">
                        <img src="images-2/new/threat.webp"
                            alt="Quisque dictum eget accumsan dignissim. Quisque">
                    </a>
                    <div class="donation-card__content">
                        <h3 class="donation-card__title"><a href="#">Threat Intelligence</a></h3>
                        <p>Effective threat intelligence equips organizations to take proactive action, addressing cyber threats before they cause harm, and ensuring responsive strategies for enhanced protection.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="owl-item active" style="width: 370px; margin-right: 30px;">
            <div class="item wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="00ms"
                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                <div class="donation-card @@extraClassName">
                    <div class="donation-card__bg"></div>
                    <a href="#" class="donation-card__image">
                        <img src="images-2/new/professional-does-ai-systems-checkup.jpg"
                            alt="Quisque dictum eget accumsan dignissim. Quisque">
                    </a>
                    <div class="donation-card__content">
                        <h3 class="donation-card__title"><a href="#">Human Security</a></h3>
                        <p>Organizations benefit from combining technology and trained staff to develop comprehensive strategies that address information security and foster cyber-aware cultures within their teams.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="owl-item" style="width: 370px; margin-right: 30px;">
            <div class="item wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="00ms"
                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                <div class="donation-card @@extraClassName">
                    <div class="donation-card__bg"></div>
                    <a href="#" class="donation-card__image">
                        <img src="images-2/new/close-up-programmer-typing-laptop.webp"
                            alt="Quisque dictum eget accumsan dignissim. Quisque">
                    </a>
                    <div class="donation-card__content">
                        <h3 class="donation-card__title"><a href="#">Cyber Defense</a></h3>
                        <p>CyberShelter's Managed SOC ensures complete protection by offering continuous monitoring, incident response, and expert support to alleviate security management challenges for businesses.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="owl-item" style="width: 370px; margin-right: 30px;">
            <div class="item wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="00ms"
                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                <div class="donation-card @@extraClassName">
                    <div class="donation-card__bg"></div>
                    <a href="#" class="donation-card__image">
                        <img src="images-2/new/ransome.webp"
                            alt="Quisque dictum eget accumsan dignissim. Quisque">
                    </a>
                    <div class="donation-card__content">
                        <h3 class="donation-card__title"><a href="#">Ransomware Management</a></h3>
                        <p>Organizations leverage CyberShelter's ransomware mitigation strategies to stay prepared, ensuring that systems are equipped to withstand and recover from disruptive ransomware attacks.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="owl-item" style="width: 370px; margin-right: 30px;">
            <div class="item wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="00ms"
                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                <div class="donation-card @@extraClassName">
                    <div class="donation-card__bg"></div>
                    <a href="#" class="donation-card__image">
                        <img src="images-2/new/hacker-cracking-binary-code-data-security.jpg"
                            alt="Quisque dictum eget accumsan dignissim. Quisque">
                    </a>
                    <div class="donation-card__content">
                        <h3 class="donation-card__title"><a href="#">Security Finetuning</a></h3>
                        <p>Our security finetuning services optimize existing systems, ensuring advanced protection, efficient functionality, and resilience against emerging cyber threats and vulnerabilities.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="owl-item" style="width: 370px; margin-right: 30px;">
            <div class="item wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="00ms"
                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                <div class="donation-card @@extraClassName">
                    <div class="donation-card__bg"></div>
                    <a href="#" class="donation-card__image">
                        <img src="images-2/new/close-up-programmer-typing-keyboard.webp"
                            alt="Quisque dictum eget accumsan dignissim. Quisque">
                    </a>
                    <div class="donation-card__content">
                        <h3 class="donation-card__title"><a href="#">Continuous PenTesting</a></h3>
                        <p>Our state-of-the-art Continuous PenTesting ensures your external systems are routinely assessed for vulnerabilities, providing unmatched security and confidence in your defenses.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="owl-item" style="width: 370px; margin-right: 30px;">
            <div class="item wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="00ms"
                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                <div class="donation-card @@extraClassName">
                    <div class="donation-card__bg"></div>
                    <a href="#" class="donation-card__image">
                        <img src="images-2/new/scene-with-business-person-working-futuristic-office-job.webp"
                            alt="Quisque dictum eget accumsan dignissim. Quisque">
                    </a>
                    <div class="donation-card__content">
                        <h3 class="donation-card__title"><a href="#">Supply Chain Security</a></h3>
                        <p>CyberShelter offers comprehensive support for assessing and managing risks within vendor and third-party relationships to ensure complete security for your organization.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="owl-item" style="width: 370px; margin-right: 30px;">
            <div class="item wow fadeInUp animated" data-wow-duration="1500ms" data-wow-delay="00ms"
                style="visibility: visible; animation-duration: 1500ms; animation-delay: 0ms; animation-name: fadeInUp;">
                <div class="donation-card @@extraClassName">
                    <div class="donation-card__bg"></div>
                    <a href="#" class="donation-card__image">
                        <img src="images-2/new/professional-does-ai-systems-checkup.jpg"
                            alt="Quisque dictum eget accumsan dignissim. Quisque">
                    </a>
                    <div class="donation-card__content">
                        <h3 class="donation-card__title"><a href="#">AD Security</a></h3>
                        <p>Active Directory vulnerabilities demand expert focus. CyberShelter specializes in AD security strategies that secure authentication and account management systems across organizations.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

            <div class="owl-dots disabled"></div>
        </div>
 
    </div>
</section>

 
<section class="testimonials-three section-space">
    <div class="container">
        <div class="row">
            <div class="col-xl-6">
                <div class="testimonials-three__content">
                    <div class="sec-title">
                        <h6 class="sec-title__tagline @@extraClassName">TESTIMONIALS</h6>
                        <h3 class="sec-title__title">What They’re Saying About <span class="sec-title__title__inner">Cyber Shelter</span></h3>
                    </div>
                    <div class="testimonials-three__content__text" style="color:#fff!important;">We provide advanced cybersecurity solutions to protect your business from evolving digital threats. Here's what our clients have to say about our services.</div>
                    <div class="testimonials-three__custome-navs"></div>
                </div>
            </div>
            <div class="col-xl-6">
                <div class="cleenhearts-stretch-element-inside-column">
                    <div class="testimonials-three__carousel cleenhearts-owl__carousel owl-theme owl-carousel" data-owl-options='{
                        "items": 1,
                        "margin": 30,
                        "smartSpeed": 700,
                        "loop":true,
                        "autoplay": 6000,
                        "stagePadding": 186,
                        "nav":true,
                        "navContainer": ".testimonials-three__custome-navs",
                        "dots":false,
                        "navText": ["<span class=\"icon-arrow-left\"></span>","<span class=\"icon-arrow-right\"></span>"],
                        "responsive":{
                            "0":{
                                "items": 1,
                                "stagePadding": 0
                            },
                            "768":{
                                "items": 1,
                                "stagePadding": 120
                            },
                            "992":{
                                "items": 1,
                                "stagePadding": 186
                            },
                            "1200":{
                                "items": 1,
                                "stagePadding": 30
                            },
                            "1360":{
                                "items": 1,
                                "stagePadding": 50
                            },
                            "1600":{
                                "items": 1,
                                "stagePadding": 186
                            }
                        }
                    }'>
                        <div class="item wow fadeInUp" data-wow-duration="1500ms" data-wow-delay="00ms">
                            <div class="testimonials-card @@extraClassName">
                                <div class="testimonials-card__bg" style="background-image: url('assets/images/backgrounds/testimonial-bg-2.png');"></div>
                                <div class="testimonials-card__top">
                                    <div class="testimonials-card__quote">
                                        <span class="icon-quote-2"></span>
                                    </div>
                                </div>
                                <div class="testimonials-card__content">
                                    <p class="testimonials-card__text">Cyber Shelter's solutions have significantly enhanced our network security. Their team is responsive and highly knowledgeable, ensuring our data is safe from evolving threats.</p>
                                    <div class="testimonials-card__info">
                                        <div class="testimonials-card__info__left">
                                            <h4 class="testimonials-card__name">Kenneth S. Fisher</h4>
                                            <span class="testimonials-card__designation">CIO, Tech Innovators</span>
                                        </div>
                                        <div class="cleenhearts-ratings testimonials-card__rattings">
                                            <span class="icon-star"></span><span class="icon-star"></span><span class="icon-star"></span><span class="icon-star"></span><span class="icon-star"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="item wow fadeInUp" data-wow-duration="1500ms" data-wow-delay="200ms">
                            <div class="testimonials-card @@extraClassName">
                                <div class="testimonials-card__bg" style="background-image: url('assets/images/backgrounds/testimonial-bg-2.png');"></div>
                                <div class="testimonials-card__top">
                                    <div class="testimonials-card__quote">
                                        <span class="icon-quote-2"></span>
                                    </div>
                                </div>
                                <div class="testimonials-card__content">
                                    <p class="testimonials-card__text">Thanks to Cyber Shelter, we now have a robust defense against cyberattacks. Their proactive approach to cybersecurity has been invaluable for our organization.</p>
                                    <div class="testimonials-card__info">
                                        <div class="testimonials-card__info__left">
                                            <h4 class="testimonials-card__name">Cedric J. Coggins</h4>
                                            <span class="testimonials-card__designation">Security Analyst, Global Enterprises</span>
                                        </div>
                                        <div class="cleenhearts-ratings testimonials-card__rattings">
                                            <span class="icon-star"></span><span class="icon-star"></span><span class="icon-star"></span><span class="icon-star"></span><span class="icon-star"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="testimonials-one section-space" id="testimonial">
              <div class="testimonials-one__bg cleenhearts-jarallax" data-jarallax="" data-speed="0.3" data-imgposition="50% -100%" style="background-image: none; z-index: 0;" data-jarallax-original-styles="background-image: url(assets/images/backgrounds/testimonial-bg-1.jpg);"><div id="jarallax-container-1" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; overflow: hidden; z-index: -100;"><div style="background-position: 50% 50%; background-size: cover; background-repeat: no-repeat; background-image: url(&quot;file:///C:/Users/<USER>/Desktop/british-academy-new/assets/images/backgrounds/testimonial-bg-1.jpg&quot;); position: fixed; top: 0px; left: 0px; width: 1408px; height: 845.195px; overflow: hidden; pointer-events: none; transform-style: preserve-3d; backface-visibility: hidden; will-change: transform, opacity; margin-top: -11.0977px; transform: translate3d(0px, 54.2883px, 0px);"></div></div></div><!-- /.testimonial-one__bg -->
            <div class="container">
                <div class="testimonials-one__wrapper">
                    <div class="testimonials-one__top">
                        <div class="row gutter-y-30 align-items-center text-center">
                            <div class="col-lg-12">
                                <div class="sec-title">


                                    <h3 class="sec-title__title">Get In Touch with Our Team  <span class="sec-title__title__inner">Today!</span></h3>
                                </div>
                            </div>
                 
                        </div>
                        
                        <div class="row gutter-y-40 align-items-center text-center">
                        <div class="col-xl-12">
                            <div class="footer-four__info">
                                
                                <div class="footer-four__info__item">
                                    <div class="footer-four__info__icon">
                                        <span class="icon-phone"></span>
                                    </div>
                                    <a href="tel:+910235203223" class="footer-four__info__text">+91 7235 203 223</a>
                                </div>
                                 <div class="footer-four__info__item">
                                    <div class="footer-four__info__icon">
                                        <span class="icon-phone"></span>
                                    </div>
                                    <a href="tel:+910235203223" class="footer-four__info__text">+91 9987643358</a>
                                </div>
                                <div class="footer-four__info__item">
                                    <div class="footer-four__info__icon">
                                        <span class="icon-envelope"></span>
                                    </div>
                                    <a href="/cdn-cgi/l/email-protection#3d54535b527d5e5158585355585c4f494e135e5250" class="footer-four__info__text"><span class="__cf_email__" data-cfemail="b8d1d6ded7f8ddc0d9d5c8d4dd96dbd7d5">[email&#160;protected]</span></a>
                                </div>
                                
                                
                            </div>
                        </div>
                                                <div class="col-xl-12" style="display:flex;justify-content:center;">
                            <div class="footer-four__logo">
    <a href="contact.html" class="cleenhearts-btn-two" style="background-color: transparent !important;">
        Get a Demo
        <span class="icon-paper-plane"></span>
    </a>
</div>

                        </div>

                    </div>
                    </div>
                </div>
            </div>
        
        </section>
        
        



<section class="blog-three section-space">
    <div class="container">
        <div class="sec-title">
            <h6 class="sec-title__tagline sec-title__tagline--center">Latest Cybersecurity News</h6>
            <h3 class="sec-title__title">Latest News &amp; Articles From Secure <span class="sec-title__title__inner">Reading</span></h3>
        </div>
        <div class="row gutter-y-30">
    <div class="col-lg-6">
        <div class="blog-card-three">
            <div class="blog-card-three__bg" ></div>
            <a href="#" class="blog-card-three__image">
                <img src="images-2/news-1.webp" alt="Cybersecurity Threats and Solutions">
                <div class="blog-card-three__date"><span>03</span> <span>SEP</span></div>
            </a>
            <div class="blog-card-three__content">
                <h3 class="blog-card-three__title"><a href="#">Cybersecurity Threats and Solutions in 2024</a></h3>
                <a href="#" class="cleenhearts-btn">
                    <div class="cleenhearts-btn__icon-box">
                        <div class="cleenhearts-btn__icon-box__inner"><span class="icon-duble-arrow"></span></div>
                    </div>
                    <span class="cleenhearts-btn__text hover-color-change" style="color: #fff !important;">Read more</span>

                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="blog-card-three">
            <div class="blog-card-three__bg" ></div>
            <a href="#" class="blog-card-three__image">
                <img src="images-2/240_F_480884814_busW8r7P4VroKSGjCupiyBekzGT61kVF.jpg" alt="Latest Cybersecurity Breaches and How to Protect">
                <div class="blog-card-three__date"><span>09</span> <span>OCT</span></div>
            </a>
            <div class="blog-card-three__content">
                <h3 class="blog-card-three__title"><a href="#">Latest Cybersecurity Breaches and How to Protect</a></h3>
                <a href="#" class="cleenhearts-btn">
                    <div class="cleenhearts-btn__icon-box">
                        <div class="cleenhearts-btn__icon-box__inner"><span class="icon-duble-arrow"></span></div>
                    </div>
                    <span class="cleenhearts-btn__text hover-color-change" style="color: #fff !important;">Read more</span>

                </a>
            </div>
        </div>
    </div>
</div>

    </div>
</section>




   <section class="events-gallery">
    <div class="events-gallery__carousel cleenhearts-owl__carousel cleenhearts-owl__carousel--basic-nav owl-carousel" data-owl-options='{
        "loop": true,
        "items": 5,
        "autoplay": true,
        "autoplayTimeout": 7000,
        "smartSpeed": 1000,
        "nav": false,
        "navText": ["<span class=\"icon-left-arrow\"></span>","<span class=\"icon-right-arrow\"></span>"],
        "dots": true,
        "margin": 0,
        "responsive": {
            "0": {
                "items": 1
            },
            "768": {
                "items": 2
            },
            "992": {
                "items": 3
            },
            "1200": {
                "items": 4
            },
            "1500": {
                "items": 5
            }
        }
    }'>
        <div class="item">
            <div class="events-gallery__item">
                <div class="events-gallery__image">
                    <img src="images-2/new/insta-6.JPG" alt="Explore Instagram" class="events-gallery__image__img">
                    <a href="#" class="events-gallery__link">
                        <img src="assets/images/resources/instagram.png" alt="instagram">
                        <span>Explore Instagram</span>
                    </a>
                </div>
            </div>
        </div>
        <div class="item">
            <div class="events-gallery__item">
                <div class="events-gallery__image">
                    <img src="images-2/new/insta-2.JPG" alt="Explore Instagram" class="events-gallery__image__img">
                    <a href="#" class="events-gallery__link">
                        <img src="assets/images/resources/instagram.png" alt="instagram">
                        <span>Explore Instagram</span>
                    </a>
                </div>
            </div>
        </div>
        <div class="item">
            <div class="events-gallery__item">
                <div class="events-gallery__image">
                    <img src="images-2/new/insta-3.JPG" alt="Explore Instagram" class="events-gallery__image__img">
                    <a href="#" class="events-gallery__link">
                        <img src="assets/images/resources/instagram.png" alt="instagram">
                        <span>Explore Instagram</span>
                    </a>
                </div>
            </div>
        </div>
        <div class="item">
            <div class="events-gallery__item">
                <div class="events-gallery__image">
                    <img src="images-2/new/insta-4.JPG" alt="Explore Instagram" class="events-gallery__image__img">
                    <a href="#" class="events-gallery__link">
                        <img src="assets/images/resources/instagram.png" alt="instagram">
                        <span>Explore Instagram</span>
                    </a>
                </div>
            </div>
        </div>
        <div class="item">
            <div class="events-gallery__item">
                <div class="events-gallery__image">
                    <img src="images-2/new/insta-5.JPG" alt="Explore Instagram" class="events-gallery__image__img">
                    <a href="#" class="events-gallery__link">
                        <img src="assets/images/resources/instagram.png" alt="instagram">
                        <span>Explore Instagram</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
   </section>







<footer class="footer-three footer-two main-footer">
    <div class="main-footer__top">
        <div class="container">
            <div class="row gutter-y-30">
                <div class="col-md-12 col-xl-3 wow fadeInUp" data-wow-duration="1500ms" data-wow-delay="00ms">
                    <div class="about-info-two__logo">
                        <img src="images-2/new/CyberShelter.png" alt="logo-dark" width="200">
                    </div>
                </div>
                <div class="col-xl-3 col-md-5 wow fadeInUp" data-wow-duration="1500ms" data-wow-delay="200ms">
                    <div class="footer-widget footer-widget--contact">
                        <h2 class="footer-widget__title">Get in touch!</h2>
                        <ul class="list-unstyled footer-widget__info">
                            <li>
                                <span class="icon-location"></span>
                                <address>901 N Pitt Str., Suite 170 Alexandria, USA</address>
                            </li>
                            <li>
                                <span class="icon-phone"></span><a href="tel:(406)555-0120">(*************</a>
                            </li>
                            <li>
                                <span class="icon-envelope"></span><a href="/cdn-cgi/l/email-protection#0f616a6a6b676a637f4f6a776e627f636a">needhelp@example</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-3 wow fadeInUp" data-wow-duration="1500ms" data-wow-delay="400ms">
                    <div class="footer-widget footer-widget--links">
                        <h2 class="footer-widget__title">Quick Links</h2>
                        <ul class="list-unstyled footer-widget__links">
                            <li><a href="#">About Us</a></li>
                            <li><a href="#">Our Services</a></li>
                            <li><a href="#">Privacy Policy</a></li>
                            <li><a href="#">Terms and Conditions</a></li>
                            <li><a href="#">Contact Us</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4 col-xl-3 wow fadeInUp" data-wow-duration="1500ms" data-wow-delay="600ms">
                    <h2 class="footer-widget__title hide-social">Social Media</h2>
                    <div class="about-info-two__social social-link-two">
                        <a href="https://facebook.com/">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                            <span class="sr-only">Facebook</span>
                        </a>
                        <a href="https://twitter.com/">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                            <span class="sr-only">Twitter</span>
                        </a>
                        <a href="https://linkedin.com/" aria-hidden="true">
                            <i class="fab fa-linkedin-in"></i>
                            <span class="sr-only">Linkedin</span>
                        </a>
                        <a href="https://youtube.com/" aria-hidden="true">
                            <i class="fab fa-youtube"></i>
                            <span class="sr-only">Youtube</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="footer-three__bottom">
        <div class="container">
            <div class="footer-three__bottom__inner">
                <p class="footer-three__copyright">
                    &copy; Copyright <span class="dynamic-year"></span> Cybershelter All Rights Reserved.
                </p>
            </div>
        </div>
    </div>
</footer>


    </div>

    <div class="mobile-nav__wrapper">
        <div class="mobile-nav__overlay mobile-nav__toggler"></div>
       
        <div class="mobile-nav__content">
            <span class="mobile-nav__close mobile-nav__toggler"><i class="fa fa-times"></i></span>

            <div class="logo-box">
                <a href="index.html" aria-label="logo image"><img src="assets/images/logo-light.png" width="155" alt="" /></a>
            </div>
           
            <div class="mobile-nav__container"></div>
            

            <ul class="mobile-nav__contact list-unstyled">
                <li>
                    <i class="fa fa-envelope"></i>
                    <a href="/cdn-cgi/l/email-protection#53302a313621203b363f27362113343e323a3f7d303c3e"><span class="__cf_email__" data-cfemail="8be8f2e9eef9f8e3eee7ffeef9cbece6eae2e7a5e8e4e6">[email&#160;protected]</span></a>
                </li>
                <li>
                    <i class="fa fa-phone-alt"></i>
                    <a href="tel:(406)555-0120">(*************</a>
                </li>
            </ul>
            <div class="mobile-nav__social">
                <a href="https://facebook.com/">
                    <i class="fab fa-facebook-f" aria-hidden="true"></i>
                    <span class="sr-only">Facebook</span>
                </a>
                <a href="https://twitter.com/">
                    <i class="fab fa-twitter" aria-hidden="true"></i>
                    <span class="sr-only">Twitter</span>
                </a>
                <a href="https://linkedin.com/" aria-hidden="true">
                    <i class="fab fa-linkedin-in"></i>
                    <span class="sr-only">Linkedin</span>
                </a>
                <a href="https://youtube.com/" aria-hidden="true">
                    <i class="fab fa-youtube"></i>
                    <span class="sr-only">Youtube</span>
                </a>
            </div>
        </div>
        
    </div>
 
<div class="floating-icons">
  <a href="https://wa.me/1234567890" target="_blank" class="whatsapp-icon">
    <i class="fab fa-whatsapp"></i>
  </a>
  <a href="tel:+1234567890" class="call-icon">
    <i class="fas fa-phone-alt"></i>
  </a>
</div>  
</div>







<style>
    .header-nav .nav > li .mega-menu {
    background: rgba(17, 29, 32, 0.9); 
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2); 
    backdrop-filter: blur(5px)!important;
    -webkit-backdrop-filter: blur(5px)!important;
    border: 1px solid rgba(17, 29, 32, 0.5);
    display: flex;
    left: 0px;
    list-style: none;
    opacity: 2;
    position: absolute;
    right: 0px;
    visibility: hidden;
    width: 100%;
    margin-top: 20px;
    z-index: 9;
    padding: 30px 10px;
    gap: 50px;
}
@media (max-width: 600px) {
    #dial-main {
        margin-top: 0 !important;
    }
}

</style>
      
      
        
    <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="assets/vendors/jquery/jquery-3.7.0.min.js"></script>
    <script src="assets/vendors/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/vendors/bootstrap-select/bootstrap-select.min.js"></script>
    <script src="assets/vendors/jarallax/jarallax.min.js"></script>
    <script src="assets/vendors/jquery-ui/jquery-ui.js"></script>
    <script src="assets/vendors/jquery-ajaxchimp/jquery.ajaxchimp.min.js"></script>
    <script src="assets/vendors/jquery-appear/jquery.appear.min.js"></script>
    <script src="assets/vendors/jquery-circle-progress/jquery.circle-progress.min.js"></script>
    <script src="assets/vendors/jquery-magnific-popup/jquery.magnific-popup.min.js"></script>
    <script src="assets/vendors/jquery-validate/jquery.validate.min.js"></script>
    <script src="assets/vendors/nouislider/nouislider.min.js"></script>
    <script src="assets/vendors/tiny-slider/tiny-slider.js"></script>
    <script src="assets/vendors/wnumb/wNumb.min.js"></script>
    <script src="assets/vendors/swiper/js/swiper-bundle.min.js"></script>
    <script src="assets/vendors/owl-carousel/js/owl.carousel.min.js"></script>
    <script src="assets/vendors/wow/wow.js"></script>
    <script src="assets/vendors/imagesloaded/imagesloaded.min.js"></script>
    <script src="assets/vendors/isotope/isotope.js"></script>
    <script src="assets/vendors/countdown/countdown.min.js"></script>
    <script src="assets/vendors/jquery-circleType/jquery.circleType.js"></script>
    <script src="assets/vendors/jquery-lettering/jquery.lettering.min.js"></script>
    <script src="assets/js/cleenhearts.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.1/dist/aos.js"></script>
    <script src="assets-2/js/main.js" type="text/javascript"></script>   
    
   
   
   
   
   
    
<script>
    AOS.init();
</script>
    
    
    <script>
        // Content data for each section - RESTORED
        const contentData = [
            {
                title: "Aligning",
                subtitle1: "Cybersecurity with",
                subtitle2: "Business Goals",
                buttonText: "Know More",
                link: "#risk-assessment"
            },
            {
                title: "Strategic",
                subtitle1: "Planning with",
                subtitle2: "Security Focus",
                buttonText: "Learn More",
                link: "#strategic-planning"
            },
            {
                title: "Building",
                subtitle1: "Human Firewall",
                subtitle2: "Defense Systems",
                buttonText: "Discover More",
                link: "#employee-training"
            },
            {
                title: "Measuring",
                subtitle1: "Security Performance",
                subtitle2: "& ROI",
                buttonText: "View Details",
                link: "#security-analytics"
            },
            {
                title: "Ensuring",
                subtitle1: "Compliance &",
                subtitle2: "Regulatory Excellence",
                buttonText: "Explore More",
                link: "#compliance-center"
            },
            {
                title: "Rapid",
                subtitle1: "Incident Response",
                subtitle2: "& Recovery",
                buttonText: "Response Center",
                link: "#incident-response"
            },
            {
                title: "Global",
                subtitle1: "Infrastructure",
                subtitle2: "Protection",
                buttonText: "See Solutions",
                link: "#global-infrastructure"
            },
            {
                title: "Seamless",
                subtitle1: "Business Integration",
                subtitle2: "& Operations",
                buttonText: "Get Started",
                link: "#business-integration"
            },
            {
                title: "Continuous",
                subtitle1: "Security",
                subtitle2: "Improvement",
                buttonText: "Learn More",
                link: "#continuous-improvement"
            }
        ];

        // State management - RESTORED
        let activePoint = 0;
        let isAutoPlaying = true;
        let isTransitioning = false;
        let autoPlayInterval;

        // DOM elements
        const contentInner = document.getElementById('contentInner');
        const mainTitle = document.getElementById('mainTitle');
        const subtitle1 = document.getElementById('subtitle1');
        const subtitle2 = document.getElementById('subtitle2');
        const buttonText = document.getElementById('buttonText');
        const ctaButton = document.getElementById('ctaButton');
        const progressRing = document.getElementById('progressRing');
        const pointsContainer = document.getElementById('pointsContainer');

        // Responsive values based on screen size
        function getResponsiveValues() {
            const width = window.innerWidth;
            
            if (width <= 480) {
                return {
                    outerRadius: 250,
                    innerRadius: 230,
                    pointActiveSize: 6,
                    pointInactiveSize: 4,
                    tickLength: 12,
                    smallTickLength: 6
                };
            } else if (width <= 640) {
                return {
                    outerRadius: 250,
                    innerRadius: 230,
                    pointActiveSize: 7,
                    pointInactiveSize: 5,
                    tickLength: 13,
                    smallTickLength: 7
                };
            } else {
                return {
                    outerRadius: 250,
                    innerRadius: 230,
                    pointActiveSize: 8,
                    pointInactiveSize: 6,
                    tickLength: 15,
                    smallTickLength: 8
                };
            }
        }

        // Calculate point positions
        function getPointPosition(index, radius = 250) {
            const angle = (index * 360) / 9 - 90;
            const x = Math.cos((angle * Math.PI) / 180) * radius;
            const y = Math.sin((angle * Math.PI) / 180) * radius;
            return { x: 300 + x, y: 300 + y };
        }

        function getInnerPointPosition(index, radius = 230) {
            const angle = (index * 360) / 9 - 90;
            const x = Math.cos((angle * Math.PI) / 180) * radius;
            const y = Math.sin((angle * Math.PI) / 180) * radius;
            return { x: 300 + x, y: 300 + y };
        }

        // Update content with smooth transition - RESTORED
        function updateContent(newIndex) {
            if (isTransitioning || newIndex === activePoint) return;
            
            isTransitioning = true;
            contentInner.classList.add('transitioning');
            
            setTimeout(() => {
                const content = contentData[newIndex];
                mainTitle.textContent = content.title;
                subtitle1.textContent = content.subtitle1;
                subtitle2.textContent = content.subtitle2;
                buttonText.textContent = content.buttonText;
                ctaButton.setAttribute('data-link', content.link);
                
                activePoint = newIndex;
                updateProgressRing();
                updatePoints();
                
                contentInner.classList.remove('transitioning');
                isTransitioning = false;
            }, 200);
        }

        // Update progress ring - RESTORED
        function updateProgressRing() {
            const responsive = getResponsiveValues();
            const circumference = 2 * Math.PI * responsive.innerRadius;
            const progress = ((activePoint + 1) / 9) * circumference;
            progressRing.style.strokeDasharray = `${progress} ${circumference}`;
        }

        // Update all visual elements - RESTORED EVERYTHING EXCEPT NONAGON BORDER
        function updatePoints() {
            pointsContainer.innerHTML = '';
            const responsive = getResponsiveValues();
            
            // OUTER BORDER - Tick marks only (no points, no flow)
            const totalTicks = 72;
            for (let i = 0; i < totalTicks; i++) {
                const angle = (i * 360) / totalTicks - 90;
                const isMainPoint = i % 8 === 0;
                
                const tickLength = isMainPoint ? responsive.tickLength : responsive.smallTickLength;
                const tickStartRadius = responsive.outerRadius - (tickLength / 2);
                const tickEndRadius = responsive.outerRadius + (tickLength / 2);
                
                const startX = 300 + Math.cos((angle * Math.PI) / 180) * tickStartRadius;
                const startY = 300 + Math.sin((angle * Math.PI) / 180) * tickStartRadius;
                const endX = 300 + Math.cos((angle * Math.PI) / 180) * tickEndRadius;
                const endY = 300 + Math.sin((angle * Math.PI) / 180) * tickEndRadius;
                
                const tick = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                tick.setAttribute('x1', startX);
                tick.setAttribute('y1', startY);
                tick.setAttribute('x2', endX);
                tick.setAttribute('y2', endY);
                tick.setAttribute('class', isMainPoint ? 'tick-mark-bold' : 'tick-mark-small');
                pointsContainer.appendChild(tick);
            }
            
            // INNER BORDER - Flow indicators (RESTORED)
            for (let i = 0; i < 9; i++) {
                const currentPos = getInnerPointPosition(i, responsive.innerRadius);
                const nextPos = getInnerPointPosition((i + 1) % 9, responsive.innerRadius);
                
                const midX = (currentPos.x + nextPos.x) / 2;
                const midY = (currentPos.y + nextPos.y) / 2;
                
                const angle = Math.atan2(nextPos.y - currentPos.y, nextPos.x - currentPos.x);
                const degrees = (angle * 180) / Math.PI;
                
                const isFlowToActive = (i + 1) % 9 === activePoint;
                const isActiveFlow = i === activePoint;
                
                // Flow line
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                line.setAttribute('x1', currentPos.x * 0.95 + 300 * 0.05);
                line.setAttribute('y1', currentPos.y * 0.95 + 300 * 0.05);
                line.setAttribute('x2', nextPos.x * 0.95 + 300 * 0.05);
                line.setAttribute('y2', nextPos.y * 0.95 + 300 * 0.05);
                line.setAttribute('stroke', isFlowToActive ? '#22C55E' : isActiveFlow ? '#86EFAC' : 'rgba(255, 255, 255, 0.2)');
                line.setAttribute('stroke-width', isFlowToActive || isActiveFlow ? '2' : '1');
                if (isFlowToActive) {
                    line.setAttribute('stroke-dasharray', '4 4');
                    line.classList.add('pulse-slow');
                }
                pointsContainer.appendChild(line);
                
                // Flow arrow
                const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
                arrow.setAttribute('points', '-6,-3 6,0 -6,3');
                arrow.setAttribute('fill', isFlowToActive ? '#22C55E' : isActiveFlow ? '#86EFAC' : 'rgba(255, 255, 255, 0.3)');
                arrow.setAttribute('transform', `translate(${midX}, ${midY}) rotate(${degrees})`);
                if (isFlowToActive) {
                    arrow.classList.add('pulse-slow');
                }
                pointsContainer.appendChild(arrow);
            }
            
            // INNER BORDER - Interactive points (RESTORED)
            for (let i = 0; i < 9; i++) {
                const isActive = i === activePoint;
                const isPrevious = i === (activePoint - 1 + 9) % 9;
                
                // Glow effects
                if (isActive) {
                    const glow = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    const glowPos = getInnerPointPosition(i, responsive.innerRadius);
                    glow.setAttribute('cx', glowPos.x);
                    glow.setAttribute('cy', glowPos.y);
                    glow.setAttribute('r', responsive.pointActiveSize * 2);
                    glow.setAttribute('fill', 'rgba(34, 197, 94, 0.3)');
                    glow.classList.add('ping');
                    pointsContainer.appendChild(glow);
                }
                
                if (isPrevious) {
                    const prevGlow = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    const prevGlowPos = getInnerPointPosition(i, responsive.innerRadius);
                    prevGlow.setAttribute('cx', prevGlowPos.x);
                    prevGlow.setAttribute('cy', prevGlowPos.y);
                    prevGlow.setAttribute('r', responsive.pointActiveSize * 1.5);
                    prevGlow.setAttribute('fill', 'rgba(134, 239, 172, 0.2)');
                    prevGlow.classList.add('pulse-slow');
                    pointsContainer.appendChild(prevGlow);
                }
                
                // Main interactive point
                const innerPointPos = getInnerPointPosition(i, responsive.innerRadius);
                const innerPoint = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                innerPoint.setAttribute('cx', innerPointPos.x);
                innerPoint.setAttribute('cy', innerPointPos.y);
                innerPoint.setAttribute('r', isActive ? responsive.pointActiveSize : responsive.pointInactiveSize);
                innerPoint.setAttribute('fill', isActive ? '#22C55E' : isPrevious ? '#86EFAC' : 'rgba(255, 255, 255, 0.6)');
                innerPoint.setAttribute('stroke', 'rgba(255, 255, 255, 0.9)');
                innerPoint.setAttribute('stroke-width', '2');
                innerPoint.style.cursor = 'pointer';
                innerPoint.style.transition = 'all 0.3s ease';
                
                innerPoint.addEventListener('click', () => handlePointClick(i));
                pointsContainer.appendChild(innerPoint);
            }
            
            // NOTE: No nonagon border lines are drawn - this is the only thing removed!
        }

        // Handle point clicks - RESTORED
        function handlePointClick(index) {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
            }
            isAutoPlaying = false;
            updateContent(index);
            
            // Resume auto-play after 8 seconds
            setTimeout(() => {
                isAutoPlaying = true;
                startAutoPlay();
            }, 8000);
        }

        // Auto-play functionality - RESTORED
        function startAutoPlay() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
            }
            
            if (!isAutoPlaying) return;
            
            autoPlayInterval = setInterval(() => {
                if (!isAutoPlaying) return;
                const nextIndex = (activePoint + 1) % 9;
                updateContent(nextIndex);
            }, 4000);
        }

        // Button click handler - RESTORED
        ctaButton.addEventListener('click', function() {
            const link = this.getAttribute('data-link');
            const currentContent = contentData[activePoint];
            
            if (link && link.startsWith('#')) {
                const targetElement = document.querySelector(link);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                } else {
                    alert(`${currentContent.buttonText} - ${currentContent.title}\nSection: ${link}`);
                }
            } else if (link) {
                window.open(link, '_blank');
            } else {
                alert(`${currentContent.buttonText} - ${currentContent.title}`);
            }
        });

        // Update SVG dimensions for responsiveness
        function updateSVGDimensions() {
            const responsive = getResponsiveValues();
            
            const outerCircle = document.querySelector('circle[r="250"]');
            const innerCircle = document.querySelector('circle[r="230"]');
            const progressRing = document.getElementById('progressRing');
            
            if (outerCircle) outerCircle.setAttribute('r', responsive.outerRadius);
            if (innerCircle) innerCircle.setAttribute('r', responsive.innerRadius);
            if (progressRing) progressRing.setAttribute('r', responsive.innerRadius);
        }

        // Initialize everything - RESTORED
        function init() {
            updateSVGDimensions();
            updateProgressRing();
            updatePoints();
            startAutoPlay();
            
            // Responsive handlers
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    updateSVGDimensions();
                    updateProgressRing();
                    updatePoints();
                }, 250);
            });
            
            window.addEventListener('orientationchange', () => {
                setTimeout(() => {
                    updateSVGDimensions();
                    updateProgressRing();
                    updatePoints();
                }, 300);
            });
        }

        // Start the application
        init();
    </script>
    
    

<script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"94e821220e267e69","version":"2025.6.2","r":1,"token":"d21a109031b24a7fb559cf38954e64ff","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}}}' crossorigin="anonymous"></script>
</body>

</html>